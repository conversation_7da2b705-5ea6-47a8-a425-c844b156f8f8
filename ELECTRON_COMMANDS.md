# Electron Commands Guide

This document provides a comprehensive guide for running and building the TIPY Textile Design CAD application using Electron.

## Prerequisites

Before running any Electron commands, ensure you have:
- Node.js (v16 or higher) installed
- All dependencies installed: `npm install`

## Development Commands

### Start Development Mode
```bash
npm run electron-dev
```
**What it does:**
- Starts the Vite development server on `http://localhost:5174`
- Waits for the server to be ready
- Launches Electron pointing to the development server
- Enables hot reloading for development
- Opens DevTools automatically in development mode

**Use this when:** You're actively developing and want to see changes in real-time.

### Start Vite Dev Server Only
```bash
npm run dev
```
**What it does:**
- Starts only the Vite development server
- Opens the app in your web browser
- No Electron wrapper

**Use this when:** You want to test the web version or debug in browser DevTools.

## Production Commands

### Build and Run Production Version
```bash
npm run electron-build
```
**What it does:**
- Builds the production version using Vite
- Creates optimized, minified files in the `dist/` folder
- Launches <PERSON><PERSON><PERSON> with the built files

**Use this when:** You want to test the production version locally.

### Launch Electron (Pre-built)
```bash
npm run electron
```
**What it does:**
- Launches Electron directly using existing built files
- Requires the app to be built first (`npm run build`)

**Use this when:** You have already built the app and just want to launch it.

### Build Only (No Launch)
```bash
npm run build
```
**What it does:**
- Creates production build files in `dist/` folder
- Does not launch the application

## Distribution Commands

### Build for Current Platform
```bash
npm run dist
```
**What it does:**
- Builds the app for your current operating system
- Creates distributable packages in `final-build/` folder
- Includes installer/package files

### Build for Windows
```bash
npm run dist-win
```
**What it does:**
- Creates Windows installer (.exe)
- Generates NSIS installer package
- Output: `final-build/TIPY - Textile Design CAD Setup.exe`

### Build for macOS
```bash
npm run dist-mac
```
**What it does:**
- Creates macOS disk image (.dmg)
- Packages the app for macOS distribution
- Output: `final-build/TIPY - Textile Design CAD.dmg`

### Build for Linux
```bash
npm run dist-linux
```
**What it does:**
- Creates Linux AppImage
- Portable application format for Linux
- Output: `final-build/TIPY - Textile Design CAD.AppImage`

## Utility Commands

### Clean Build Files
```bash
npm run clean
```
**What it does:**
- Removes `dist/` and `final-build/` directories
- Cleans up all build artifacts

**Use this when:** You want to start fresh or troubleshoot build issues.

### Preview Production Build
```bash
npm run preview
```
**What it does:**
- Serves the built files locally for testing
- Runs on `http://localhost:4173`
- No Electron wrapper

## Common Workflows

### Development Workflow
1. `npm install` (first time only)
2. `npm run electron-dev` (for development with Electron)
3. Make changes to your code
4. Changes automatically reload in Electron

### Testing Production Build
1. `npm run electron-build` (builds and runs)
2. Test the production version
3. `npm run clean` (if you need to clean up)

### Creating Distribution Package
1. `npm run dist-win` (for Windows)
2. Find the installer in `final-build/` folder
3. Distribute the installer to users

## Troubleshooting

### Common Issues

**Electron won't start:**
- Make sure all dependencies are installed: `npm install`
- Try cleaning and rebuilding: `npm run clean && npm run electron-build`

**Build fails:**
- Check Node.js version (should be v16+)
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`

**Development server issues:**
- Check if port 5174 is available
- Try restarting: Stop the process and run `npm run electron-dev` again

**Distribution build fails:**
- Ensure you have sufficient disk space
- Check platform-specific requirements (e.g., code signing for macOS)

### Platform-Specific Notes

**Windows:**
- Requires Windows 10 or later for the built application
- NSIS installer is created for distribution

**macOS:**
- May require code signing for distribution
- DMG file is created for easy installation

**Linux:**
- AppImage format works on most Linux distributions
- No installation required, just make executable and run

## File Structure After Build

```
final-build/
├── TIPY - Textile Design CAD Setup.exe    (Windows installer)
├── TIPY - Textile Design CAD.dmg          (macOS disk image)
├── TIPY - Textile Design CAD.AppImage     (Linux portable app)
└── win-unpacked/                          (Windows unpacked files)
    └── TIPY - Textile Design CAD.exe      (Windows executable)
```

## Environment Variables

The application automatically detects the environment:
- `NODE_ENV=development` - Development mode with DevTools
- `NODE_ENV=production` - Production mode (default for built apps)

## Support

For issues with Electron commands:
1. Check this documentation
2. Verify Node.js and npm versions
3. Try the troubleshooting steps above
4. Check the console output for specific error messages
