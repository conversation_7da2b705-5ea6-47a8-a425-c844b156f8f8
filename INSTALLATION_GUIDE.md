# TIPY - Textile Design CAD Installation Guide

## For Your Client

### Quick Installation (Recommended)

1. **Download the installer**: `TIPY - Textile Design CAD Setup 1.0.0.exe`
2. **Run the installer**: Double-click the .exe file
3. **Follow the installation wizard**: Click "Next" through the installation steps
4. **Launch the application**: The app will be available in your Start Menu or Desktop

### Manual Installation (Alternative)

If you prefer not to use the installer:

1. **Download the portable version**: Extract the `win-unpacked` folder
2. **Run the application**: Double-click `TIPY - Textile Design CAD.exe` inside the folder
3. **Create a shortcut**: Right-click the .exe file and select "Create shortcut" for easy access

## System Requirements

- **Operating System**: Windows 10 or later (64-bit)
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: 500MB free space
- **Graphics**: DirectX 11 compatible graphics card

## Features

### Color Palette
- Click any predefined color to open the thread information popup
- Customize colors using the color picker
- Set thread count and specifications

### Drawing Tools
- **Warp (Vertical)**: Draw vertical threads
- **Weft (Horizontal)**: Draw horizontal threads
- Switch between tools using the left panel

### Canvas Controls
- **Zoom**: Use mouse wheel or zoom controls (top-right)
- **Pan**: Click and drag to move around the canvas
- **Reset**: Use the reset button to return to default view
- **Grid**: Adjust grid size for different design resolutions

## Troubleshooting

### If the app doesn't start:
1. Make sure you have Windows 10 or later
2. Try running as administrator (right-click → "Run as administrator")
3. Check if Windows Defender or antivirus is blocking the app

### If you see security warnings:
- This is normal for new applications
- Click "More info" → "Run anyway" if Windows SmartScreen appears
- The app is safe and doesn't require internet connection

### Performance Issues:
- Close other heavy applications
- Ensure you have enough free RAM
- Update your graphics drivers

## Support

For technical support or questions about the application, please contact your software provider.

---

**Application Version**: 1.0.1
**Build Date**: $(Get-Date -Format "yyyy-MM-dd")
**Platform**: Windows 64-bit

---

## For Developers

### Development Setup

1. **Prerequisites**: Node.js 18+ and npm
2. **Install dependencies**: `npm install`
3. **Development mode**: `npm run electron-dev`
4. **Build for production**: `npm run dist-win`

### Build Troubleshooting

#### Common Issues

1. **Port already in use**: If you get a port error, try changing the port in `vite.config.js`
2. **Module not found**: Run `npm install` to ensure all dependencies are installed
3. **Build fails**: Make sure you have the latest Node.js version installed
4. **Blank screen in executable**: This has been fixed with proper Vite configuration for Electron
5. **JavaScript errors**: Error handling has been added to show meaningful error messages

#### Build Issues

If the build fails, try:
1. Clean previous builds: `npm run clean`
2. Delete `node_modules` and `package-lock.json`
3. Run `npm install` again
4. Try building with `npm run build`

#### Fixed Issues (v1.0.1)

- ✅ Fixed blank screen issue in packaged executable
- ✅ Added proper error handling for renderer crashes
- ✅ Configured Vite for Electron compatibility with `base: './'`
- ✅ Removed DevTools from production builds
- ✅ Added clean script for build artifacts
- ✅ Improved error reporting for debugging
