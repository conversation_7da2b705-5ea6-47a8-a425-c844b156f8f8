# TIPY - Textile Design CAD

A modern textile design CAD application built with React, Vite, and Electron.

## Features

- Interactive textile design canvas with zoom and pan
- Warp and Weft drawing tools
- Color palette with predefined colors
- Custom color picker with thread information popup
- Grid-based design system
- Desktop application support via Electron

## Development Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation
```bash
npm install
```

### Running the Application

#### Web Version (Development)
```bash
npm run dev
```
This will start the Vite development server at `http://localhost:5173`

#### Desktop App (Development)
```bash
npm run electron-dev
```
This will start both the Vite dev server and Electron app simultaneously.

#### Desktop App (Production Build)
```bash
npm run electron-build
```
This builds the web app and runs it in Electron.

### Building for Distribution

#### Build Web Version
```bash
npm run build
```

#### Build Desktop Apps
```bash
# Build for current platform
npm run dist

# Build for Windows
npm run dist-win

# Build for macOS
npm run dist-mac

# Build for Linux
npm run dist-linux
```

## Usage

### Color Facets
- Click any predefined color in the color palette to open the thread information popup
- Use the "Custom Color..." button to create custom colors
- The popup shows color name, color picker, and thread settings

### Drawing Tools
- **Warp (Vertical)**: Draw vertical threads
- **Weft (Horizontal)**: Draw horizontal threads
- Use the grid settings to adjust the design resolution

### Canvas Controls
- **Mouse wheel**: Zoom in/out
- **Drag**: Pan the canvas
- **Zoom controls**: Use the controls in the top-right corner
- **Reset**: Return to default view

## Technology Stack

- **Frontend**: React 19, Vite 6
- **Canvas**: Konva.js with React-Konva
- **Styling**: Tailwind CSS
- **Desktop**: Electron
- **Build Tools**: Vite, Electron Builder
