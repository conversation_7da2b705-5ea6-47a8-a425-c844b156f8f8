const { app, BrowserWindow, <PERSON>u } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development'

async function createWindow() {
  // Create the browser window
  const mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/vite.svg'), // You can replace with your app icon
    title: 'TIPY - Textile Design CAD',
    show: true, // Show immediately
    center: true, // Center the window
    alwaysOnTop: isDev, // Keep on top in development
    focusable: true
  })

  // Load the app
  if (isDev) {
    console.log('Loading development server...')
    const ports = ['5174', '5175', '5173', '5176']
    let loaded = false

    for (const port of ports) {
      try {
        await mainWindow.loadURL(`http://localhost:${port}`)
        console.log(`Successfully loaded development server on port ${port}`)
        loaded = true
        break
      } catch (error) {
        console.log(`Failed to load port ${port}, trying next...`)
      }
    }

    if (!loaded) {
      console.error('Failed to load development server on any port')
    }

    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    // In production, load from the packaged app
    const indexPath = path.join(__dirname, '../dist/index.html')

    mainWindow.loadFile(indexPath).catch(error => {
      console.error('Failed to load index.html:', error)
      // Show error dialog to user
      const { dialog } = require('electron')
      dialog.showErrorBox('Loading Error', `Failed to load application: ${error.message}`)
    })
  }

  // Show window immediately in development, or when ready in production
  if (isDev) {
    mainWindow.show()
    console.log('Window shown')
  } else {
    mainWindow.once('ready-to-show', () => {
      mainWindow.show()
    })
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    // Dereference the window object
    mainWindow = null
  })

  // Add error handling for web contents
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', errorCode, errorDescription, validatedURL)
  })

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page loaded successfully')
  })

  mainWindow.webContents.on('dom-ready', () => {
    console.log('DOM ready')
  })

  // Handle renderer process crashes
  mainWindow.webContents.on('crashed', (event, killed) => {
    console.error('Renderer process crashed:', { killed })
    const { dialog } = require('electron')
    dialog.showErrorBox('Application Crashed', 'The application has crashed. Please restart.')
  })

  // Handle unresponsive renderer
  mainWindow.on('unresponsive', () => {
    console.error('Renderer process became unresponsive')
  })

  return mainWindow
}

// Application menu removed - using custom in-app menu instead
// The menu is now handled within the React application

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  await createWindow()

  // Remove the application menu (the black bar with File, Edit, View, Tools, Help)
  Menu.setApplicationMenu(null)

  app.on('activate', async () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      await createWindow()
    }
  })
})

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
  })
})
