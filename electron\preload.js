const { contextBridge, ipc<PERSON>enderer } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  saveDesign: (data) => ipcRenderer.invoke('save-design', data),
  loadDesign: () => ipcRenderer.invoke('load-design'),
  exportPNG: (data) => ipcRenderer.invoke('export-png', data),
  exportPDF: (data) => ipcRenderer.invoke('export-pdf', data),
  
  // App info
  getVersion: () => ipcRenderer.invoke('get-version'),
  
  // Window controls
  minimize: () => ipcRenderer.invoke('minimize-window'),
  maximize: () => ipcRenderer.invoke('maximize-window'),
  close: () => ipcRenderer.invoke('close-window'),
  
  // Event listeners
  onMenuAction: (callback) => ipcRenderer.on('menu-action', callback),
  removeAllListeners: (channel) => ipc<PERSON>enderer.removeAllListeners(channel)
})

// Prevent the renderer process from accessing Node.js
delete window.require
delete window.exports
delete window.module
