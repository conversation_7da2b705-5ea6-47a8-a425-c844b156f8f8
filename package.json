{"name": "tipy", "private": true, "version": "1.0.1", "description": "TIPY - Textile Design CAD Application for creating and designing textile patterns", "author": "Your Company Name", "type": "module", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5174 && npx cross-env NODE_ENV=development npx electron .\"", "electron-build": "npm run build && electron .", "dist": "npm run build && electron-builder", "dist-win": "npm run build && electron-builder --win", "dist-mac": "npm run build && electron-builder --mac", "dist-linux": "npm run build && electron-builder --linux", "clean": "<PERSON><PERSON><PERSON> dist final-build"}, "dependencies": {"konva": "^9.3.20", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-konva": "^19.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^36.3.2", "electron-builder": "^26.0.12", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.4", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "vite": "^6.3.5", "wait-on": "^8.0.3"}, "build": {"appId": "com.tipy.textile-design-cad", "productName": "TIPY - Textile Design CAD", "directories": {"output": "final-build"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "asar": false, "mac": {"category": "public.app-category.graphics-design", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}