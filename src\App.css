/* Custom styles for Fabric Design CAD */

/* Ensure the app takes full height */
#root {
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* Custom scrollbar for sidebars */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Canvas specific styles */
.canvas-container {
  position: relative;
  overflow: hidden;
}

.canvas-content {
  transition: transform 0.1s ease-out;
}

/* Zoom controls styling */
.zoom-controls {
  backdrop-filter: blur(4px);
  background-color: rgba(255, 255, 255, 0.9);
}

.zoom-controls button {
  transition: all 0.2s ease;
  font-weight: bold;
  min-width: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-controls button:hover {
  background-color: #e5e7eb;
  transform: scale(1.05);
}

/* Center axes styling */
.center-axes {
  pointer-events: none;
  z-index: 10;
}

/* Prevent text selection on UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Resizable Panel Styles */
.resize-handle {
  background-color: #e5e7eb;
  border: 1px solid #d1d5db;
  cursor: col-resize;
  transition: background-color 0.2s ease;
  position: relative;
  z-index: 10;
  width: 6px;
  flex-shrink: 0;
}

.resize-handle:hover {
  background-color: #d1d5db;
}

.resize-handle.dragging {
  background-color: #3b82f6;
  cursor: col-resize;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 3px;
  height: 20px;
  background-color: #9ca3af;
  border-radius: 2px;
}

.resize-handle:hover::before {
  background-color: #6b7280;
}

.resize-handle.dragging::before {
  background-color: #ffffff;
}

/* Panel transition for smooth resizing */
.resizable-panel {
  transition: width 0.1s ease-out;
}

/* Disable transitions during drag */
.dragging .resizable-panel {
  transition: none;
}