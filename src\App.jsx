import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Line } from 'react-konva'
import DesignSpecificationDialog from './components/Dialogs/DesignSpecificationDialog'
import TopMenuBar from './components/Layout/TopMenuBar'
import LeftSidebar from './components/Layout/LeftSidebar'
import RightSidebar from './components/Layout/RightSidebar'
import ResizablePanels from './components/Layout/ResizablePanels'
import DesignCanvas from './components/Canvas/DesignCanvas'
import ColorPickerDialog from './components/Dialogs/ColorPickerDialog'
import DesignInfoDialog from './components/Dialogs/DesignInfoDialog'
import ChangePasswordDialog from './components/Dialogs/ChangePasswordDialog'
import LoginCard from './components/Auth/LoginCard'
import './App.css'

function App() {
  // Authentication state - check localStorage on initial load
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    try {
      return localStorage.getItem('tipy_authenticated') === 'true'
    } catch (error) {
      console.warn('localStorage not available:', error)
      return false
    }
  })

  const [selectedTool, setSelectedTool] = useState('Warp')
  const [gridSize, setGridSize] = useState(25)
  const [selectedColor, setSelectedColor] = useState('#000000')
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [customColor, setCustomColor] = useState('#FF0000')
  const [selectedColorName, setSelectedColorName] = useState('BLACK')
  const stageRef = useRef(null)
  const resizeObserverRef = useRef(null)
  const [stageSize, setStageSize] = useState({ width: 800, height: 600 })

  // Design Information Dialog states
  const [showDesignInfo, setShowDesignInfo] = useState(false)
  const [designInfoCollected, setDesignInfoCollected] = useState(true)
  const [designNumber, setDesignNumber] = useState('DEFAULT')
  const [designName, setDesignName] = useState('Default Design')
  const [pendingColor, setPendingColor] = useState(null)
  const [pendingColorName, setPendingColorName] = useState('')
  
  // State for saving and loading designs
  const [currentDesignId, setCurrentDesignId] = useState(null)

  // Thread drawing states
  const [numberOfThreads, setNumberOfThreads] = useState(10)
  const [drawnThreads, setDrawnThreads] = useState([])
  const [isDrawingMode, setIsDrawingMode] = useState(false)
  const [isEraserMode, setIsEraserMode] = useState(false)
  const [drawingModeInfoShown, setDrawingModeInfoShown] = useState(false)

  // Undo/Redo state management
  const [history, setHistory] = useState([[]])
  const [historyIndex, setHistoryIndex] = useState(0)

  // Design menu dropdown state
  const [showDesignMenu, setShowDesignMenu] = useState(false)

  // Design Specification popup state
  const [showDesignSpecification, setShowDesignSpecification] = useState(false)
  const [designSpecificationTab, setDesignSpecificationTab] = useState('Design Selection')

  // Additional menu dropdown states for textile modules
  const [showPurchaseMenu, setShowPurchaseMenu] = useState(false)
  const [showDyeingMenu, setShowDyeingMenu] = useState(false)
  const [showWeavingMenu, setShowWeavingMenu] = useState(false)
  const [showSuppliersMenu, setShowSuppliersMenu] = useState(false)
  const [showProcessingMenu, setShowProcessingMenu] = useState(false)
  const [showDispatchesMenu, setShowDispatchesMenu] = useState(false)
  const [showAccountsMenu, setShowAccountsMenu] = useState(false)
  const [showUtilityMenu, setShowUtilityMenu] = useState(false)

  // Change Password dialog state
  const [showChangePassword, setShowChangePassword] = useState(false)

  const colorPalette = [
    '#FF69B4', '#87CEEB', '#98FB98', '#F0E68C', '#DDA0DD',
    '#FFB6C1', '#40E0D0', '#FF6347', '#90EE90', '#F5DEB3',
    '#000080', '#8B4513', '#FF1493', '#00CED1', '#ADFF2F'
  ]

  const colorNames = {
    '#FF69B4': 'HOT PINK',
    '#87CEEB': 'SKY BLUE',
    '#98FB98': 'PALE GREEN',
    '#F0E68C': 'KHAKI',
    '#DDA0DD': 'PLUM',
    '#FFB6C1': 'LIGHT PINK',
    '#40E0D0': 'TURQUOISE',
    '#FF6347': 'TOMATO',
    '#90EE90': 'LIGHT GREEN',
    '#F5DEB3': 'WHEAT',
    '#000080': 'NAVY',
    '#8B4513': 'SADDLE BROWN',
    '#FF1493': 'DEEP PINK',
    '#00CED1': 'DARK TURQUOISE',
    '#ADFF2F': 'GREEN YELLOW',
    '#000000': 'BLACK',
    '#FF0000': 'RED'
  }

  const handleColorSelect = (color) => {
    setSelectedColor(color)
    setCustomColor(color)
    setSelectedColorName(colorNames[color] || 'CUSTOM')
    setShowColorPicker(true)
  }

  // Authentication handlers
  const handleLogin = () => {
    setIsAuthenticated(true)
    try {
      localStorage.setItem('tipy_authenticated', 'true')
    } catch (error) {
      console.warn('Could not save authentication state:', error)
    }
  }

  const handleLogout = () => {
    setIsAuthenticated(false)
    try {
      localStorage.removeItem('tipy_authenticated')
    } catch (error) {
      console.warn('Could not clear authentication state:', error)
    }
  }

  // Change Password handlers
  const handleChangePassword = () => {
    setShowChangePassword(true)
  }

  const handlePasswordChange = (newUsername, newPassword) => {
    // This function is called when password is successfully changed
    // The actual credential update is handled in the ChangePasswordDialog component
    console.log('Password changed successfully for user:', newUsername)
  }

  const handleDesignInfoSubmit = () => {
    if (designNumber.trim() && designName.trim()) {
      setDesignInfoCollected(true)
      setShowDesignInfo(false)

      // Now proceed with the color selection
      setSelectedColor(pendingColor)
      setCustomColor(pendingColor)
      setSelectedColorName(pendingColorName)
      setShowColorPicker(true)

      // Clear pending values
      setPendingColor(null)
      setPendingColorName('')
    }
  }

  const handleDesignInfoCancel = () => {
    setShowDesignInfo(false)
    setPendingColor(null)
    setPendingColorName('')
  }

  const handleCreateDesignSpecification = () => {
    if (designInfoCollected) {
      setShowDesignSpecification(true)
      setShowDesignMenu(false) // Close the design menu
    } else {
      alert('Please select a color first to enter design information.')
    }
  }

  // Helper function to close all menus
  const closeAllMenus = () => {
    setShowDesignMenu(false)
    setShowPurchaseMenu(false)
    setShowDyeingMenu(false)
    setShowWeavingMenu(false)
    setShowSuppliersMenu(false)
    setShowProcessingMenu(false)
    setShowDispatchesMenu(false)
    setShowAccountsMenu(false)
    setShowUtilityMenu(false)
  }

  // Handle stage resize with ResizeObserver for better responsiveness
  useEffect(() => {
    const handleResize = () => {
      const container = document.querySelector('.canvas-container')
      if (container) {
        const rect = container.getBoundingClientRect()
        setStageSize({
          width: rect.width,
          height: rect.height
        })
      }
    }

    // Use a timeout to ensure the DOM is ready
    const initializeResize = () => {
      // Initial size calculation
      handleResize()

      // Use ResizeObserver for better detection of container size changes
      const container = document.querySelector('.canvas-container')
      let resizeObserver = null

      if (container && window.ResizeObserver) {
        resizeObserver = new ResizeObserver((entries) => {
          for (let entry of entries) {
            const { width, height } = entry.contentRect
            if (width > 0 && height > 0) {
              setStageSize({ width, height })
            }
          }
        })
        resizeObserver.observe(container)
      }

      return resizeObserver
    }

    // Initialize after a short delay to ensure DOM is ready
    const timeoutId = setTimeout(() => {
      const resizeObserver = initializeResize()

      // Store the observer for cleanup
      if (resizeObserver) {
        resizeObserverRef.current = resizeObserver
      }
    }, 100)

    // Fallback to window resize listener
    window.addEventListener('resize', handleResize)

    return () => {
      clearTimeout(timeoutId)
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect()
        resizeObserverRef.current = null
      }
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  // Handle panel resize callback
  const handlePanelResize = useCallback(() => {
    // Trigger canvas resize after panel resize
    setTimeout(() => {
      const container = document.querySelector('.canvas-container')
      if (container) {
        const rect = container.getBoundingClientRect()
        setStageSize({
          width: rect.width,
          height: rect.height
        })
      }
    }, 50) // Small delay to ensure DOM has updated
  }, [])

  // Close all menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.menu-container')) {
        closeAllMenus()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showDesignMenu, showPurchaseMenu, showDyeingMenu, showWeavingMenu, showSuppliersMenu, showProcessingMenu, showDispatchesMenu, showAccountsMenu, showUtilityMenu])



  // History management functions
  const addToHistory = (newThreads) => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push([...newThreads])
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
    setDrawnThreads(newThreads)
  }

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1)
      setDrawnThreads(history[historyIndex - 1])
    }
  }

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1)
      setDrawnThreads(history[historyIndex + 1])
    }
  }

  const clearCanvas = () => {
    const newHistory = history.slice(0, historyIndex + 1)
    newHistory.push([])
    setHistory(newHistory)
    setHistoryIndex(newHistory.length - 1)
    setDrawnThreads([])
  }

  // Generate grid lines
  const generateGridLines = () => {
    const lines = []
    const gridExtent = 5000 // Large grid area
    const centerOffset = gridExtent / 2

    // Vertical lines
    for (let i = -centerOffset; i <= centerOffset; i += gridSize) {
      lines.push(
        <Line
          key={`v-${i}`}
          points={[i, -centerOffset, i, centerOffset]}
          stroke="#e5e7eb"
          strokeWidth={1}
        />
      )
    }

    // Horizontal lines
    for (let i = -centerOffset; i <= centerOffset; i += gridSize) {
      lines.push(
        <Line
          key={`h-${i}`}
          points={[-centerOffset, i, centerOffset, i]}
          stroke="#e5e7eb"
          strokeWidth={1}
        />
      )
    }

    return lines
  }

  // Generate center axes
  const generateAxes = () => {
    const axisExtent = 5000
    return [
      // X-axis (horizontal)
      <Line
        key="x-axis"
        points={[-axisExtent, 0, axisExtent, 0]}
        stroke="#6b7280"
        strokeWidth={1}
      />,
      // Y-axis (vertical)
      <Line
        key="y-axis"
        points={[0, -axisExtent, 0, axisExtent]}
        stroke="#6b7280"
        strokeWidth={1}
      />
    ]
  }



  // Eraser functionality
  const handleEraserClick = (e) => {
    if (!isEraserMode) return

    const stage = e.target.getStage()
    const pointer = stage.getPointerPosition()

    // Convert screen coordinates to canvas coordinates
    const transform = stage.getAbsoluteTransform().copy()
    transform.invert()
    const pos = transform.point(pointer)

    // Find threads to remove based on click position
    const threadsToRemove = drawnThreads.filter(thread => {
      if (thread.type === 'Warp') {
        // For warp threads, check if click is near the x position
        const threadX = thread.points[0]
        return Math.abs(pos.x - threadX) <= gridSize / 2
      } else {
        // For weft threads, check if click is near the y position
        const threadY = thread.points[1]
        return Math.abs(pos.y - threadY) <= gridSize / 2
      }
    })

    if (threadsToRemove.length > 0) {
      const newThreads = drawnThreads.filter(thread => !threadsToRemove.includes(thread))
      addToHistory(newThreads)
    }
  }

  // Handle canvas click for drawing threads - Color entire axis
  const handleCanvasClick = (e) => {
    if (isEraserMode) {
      handleEraserClick(e)
      return
    }

    if (!isDrawingMode) return

    const stage = e.target.getStage()
    const pointer = stage.getPointerPosition()

    // Convert screen coordinates to canvas coordinates
    const transform = stage.getAbsoluteTransform().copy()
    transform.invert()
    const pos = transform.point(pointer)

    // Snap to grid
    const snappedX = Math.round(pos.x / gridSize) * gridSize
    const snappedY = Math.round(pos.y / gridSize) * gridSize

    // Create thread group based on selected tool and number of threads
    const newThreads = []
    const threadSpacing = gridSize
    const axisExtent = 5000 // Same as grid extent for full axis coverage

    for (let i = 0; i < numberOfThreads; i++) {
      const threadId = `thread-${Date.now()}-${i}`

      if (selectedTool === 'Warp') {
        // Warp threads - Always vertical (textile industry standard)
        const x = snappedX + (i * threadSpacing)
        newThreads.push({
          id: threadId,
          type: 'Warp',
          color: selectedColor,
          colorName: selectedColorName,
          points: [x, -axisExtent, x, axisExtent], // Full vertical axis
          strokeWidth: Math.max(4, gridSize / 5)
        })
      } else {
        // Weft threads - Always horizontal (textile industry standard)
        const y = snappedY + (i * threadSpacing)
        newThreads.push({
          id: threadId,
          type: 'Weft',
          color: selectedColor,
          colorName: selectedColorName,
          points: [-axisExtent, y, axisExtent, y], // Full horizontal axis
          strokeWidth: Math.max(4, gridSize / 5)
        })
      }
    }

    const updatedThreads = [...drawnThreads, ...newThreads]
    addToHistory(updatedThreads)
  }

  // If not authenticated, show login screen
  if (!isAuthenticated) {
    return <LoginCard onLogin={handleLogin} />
  }

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Top Menu Bar */}
      <TopMenuBar
        showDesignMenu={showDesignMenu}
        setShowDesignMenu={setShowDesignMenu}
        showPurchaseMenu={showPurchaseMenu}
        setShowPurchaseMenu={setShowPurchaseMenu}
        showDyeingMenu={showDyeingMenu}
        setShowDyeingMenu={setShowDyeingMenu}
        showWeavingMenu={showWeavingMenu}
        setShowWeavingMenu={setShowWeavingMenu}
        showSuppliersMenu={showSuppliersMenu}
        setShowSuppliersMenu={setShowSuppliersMenu}
        showProcessingMenu={showProcessingMenu}
        setShowProcessingMenu={setShowProcessingMenu}
        showDispatchesMenu={showDispatchesMenu}
        setShowDispatchesMenu={setShowDispatchesMenu}
        showAccountsMenu={showAccountsMenu}
        setShowAccountsMenu={setShowAccountsMenu}
        showUtilityMenu={showUtilityMenu}
        setShowUtilityMenu={setShowUtilityMenu}
        handleCreateDesignSpecification={handleCreateDesignSpecification}
        closeAllMenus={closeAllMenus}
        handleLogout={handleLogout}
        handleChangePassword={handleChangePassword}
      />

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanels
          leftPanel={
            <LeftSidebar
              selectedTool={selectedTool}
              setSelectedTool={setSelectedTool}
              gridSize={gridSize}
              setGridSize={setGridSize}
              isDrawingMode={isDrawingMode}
              setIsDrawingMode={setIsDrawingMode}
              isEraserMode={isEraserMode}
              setIsEraserMode={setIsEraserMode}
              undo={undo}
              redo={redo}
              clearCanvas={clearCanvas}
              canUndo={historyIndex > 0}
              canRedo={historyIndex < history.length - 1}
              showColorPicker={showColorPicker}
              setShowColorPicker={setShowColorPicker}
              drawingModeInfoShown={drawingModeInfoShown}
              setDrawingModeInfoShown={setDrawingModeInfoShown}
            />
          }
          centerPanel={
            <DesignCanvas
              stageRef={stageRef}
              stageSize={stageSize}
              gridSize={gridSize}
              isDrawingMode={isDrawingMode}
              isEraserMode={isEraserMode}
              drawnThreads={drawnThreads}
              handleCanvasClick={handleCanvasClick}
              generateGridLines={generateGridLines}
              generateAxes={generateAxes}
            />
          }
          rightPanel={
            <RightSidebar
              selectedTool={selectedTool}
              setSelectedTool={setSelectedTool}
              selectedColor={selectedColor}
              selectedColorName={selectedColorName}
              numberOfThreads={numberOfThreads}
              setNumberOfThreads={setNumberOfThreads}
              gridSize={gridSize}
              stageRef={stageRef}
              isDrawingMode={isDrawingMode}
              setIsDrawingMode={setIsDrawingMode}
              colorPalette={colorPalette}
              colorNames={colorNames}
              handleColorSelect={handleColorSelect}
              designInfoCollected={designInfoCollected}
              setPendingColor={setPendingColor}
              setPendingColorName={setPendingColorName}
              setShowDesignInfo={setShowDesignInfo}
              setCustomColor={setCustomColor}
              setSelectedColorName={setSelectedColorName}
              setShowColorPicker={setShowColorPicker}
            />
          }
          initialLeftWidth={20}
          initialRightWidth={20}
          minPanelWidth={15}
          maxPanelWidth={70}
          onResize={handlePanelResize}
        />
      </div>

      {/* Design Information Dialog */}
      <DesignInfoDialog
        showDesignInfo={showDesignInfo}
        setShowDesignInfo={setShowDesignInfo}
        designNumber={designNumber}
        setDesignNumber={setDesignNumber}
        designName={designName}
        setDesignName={setDesignName}
        handleDesignInfoSubmit={handleDesignInfoSubmit}
        handleDesignInfoCancel={handleDesignInfoCancel}
      />

      {/* Color Picker Dialog */}
      <ColorPickerDialog
        showColorPicker={showColorPicker}
        setShowColorPicker={setShowColorPicker}
        selectedColorName={selectedColorName}
        customColor={customColor}
        setCustomColor={setCustomColor}
        numberOfThreads={numberOfThreads}
        setNumberOfThreads={setNumberOfThreads}
        setSelectedColor={setSelectedColor}
        setSelectedColorName={setSelectedColorName}
        setIsDrawingMode={setIsDrawingMode}
        colorNames={colorNames}
        addToFavorites={(color, name) => console.log('Add to favorites', color, name)}
        addToRecentColors={(color) => console.log('Add to recent colors', color)}
      />

      {/* Design Specification Dialog */}
      <DesignSpecificationDialog
        showDesignSpecification={showDesignSpecification}
        setShowDesignSpecification={setShowDesignSpecification}
        designSpecificationTab={designSpecificationTab}
        setDesignSpecificationTab={setDesignSpecificationTab}
        drawnThreads={drawnThreads}
        designNumber={designNumber}
        designName={designName}
        setDrawnThreads={setDrawnThreads}
        saveDesign={(designNo, designName, threads) => {
          setDesignNumber(designNo)
          setDesignName(designName)
          // Additional logic can be added here if needed
          // For now, the actual saving is done in the DesignSpecificationDialog component
        }}
        loadDesign={(designNo, designName, threads) => {
          setDesignNumber(designNo)
          setDesignName(designName)
          setDrawnThreads(threads)
        }}
      />

      {/* Change Password Dialog */}
      <ChangePasswordDialog
        showChangePassword={showChangePassword}
        setShowChangePassword={setShowChangePassword}
        onPasswordChange={handlePasswordChange}
      />
    </div>
  )
}

export default App