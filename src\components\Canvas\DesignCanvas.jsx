import React, { useState, useEffect } from 'react'
import { Stage, Layer, Line, Group, Rect } from 'react-konva'

const DesignCanvas = ({
  stageRef,
  stageSize,
  gridSize,
  isDrawingMode,
  drawnThreads,
  handleCanvasClick,
  generateGridLines,
  generateAxes
}) => {
  // Initialize state for the box dimensions
  const [boxDimensions, setBoxDimensions] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0
  });
  
  // State to track if we're in fit-to-outline mode
  const [fitToOutline, setFitToOutline] = useState(false);
  // Calculate the box dimensions based on the threads
  useEffect(() => {
    if (drawnThreads.length === 0) return;
    
    // Find the minimum and maximum coordinates of all threads
    let minX = Infinity;
    let maxX = -Infinity;
    let minY = Infinity;
    let maxY = -Infinity;
    
    drawnThreads.forEach(thread => {
      // For warp threads (vertical)
      if (thread.type === 'Warp') {
        const x = thread.points[0]; // x coordinate is the same for vertical lines
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x);
      }
      // For weft threads (horizontal)
      else if (thread.type === 'Weft') {
        const y = thread.points[1]; // y coordinate is the same for horizontal lines
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y);
      }
    });
    
    // If we have valid boundaries, update the box dimensions
    if (minX !== Infinity && maxX !== -Infinity && minY !== Infinity && maxY !== -Infinity) {
      // Add some padding around the boundaries
      const padding = gridSize * 2;
      setBoxDimensions({
        x: minX - padding,
        y: minY - padding,
        width: maxX - minX + padding * 2,
        height: maxY - minY + padding * 2
      });
    }
  }, [drawnThreads, gridSize]);
  return (
    <div className="w-full h-full bg-white relative overflow-hidden canvas-container">
      {/* Zoom Controls */}
      <div className="absolute top-4 right-4 z-10 flex flex-col space-y-2">
        <button
          onClick={() => {
            const stage = stageRef.current
            if (stage) {
              const oldScale = stage.scaleX()
              const newScale = Math.min(5, oldScale * 1.2)
              stage.scale({ x: newScale, y: newScale })
            }
          }}
          className="px-3 py-1 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 text-sm"
        >
          +
        </button>
        <button
          onClick={() => {
            const stage = stageRef.current
            if (stage) {
              const oldScale = stage.scaleX()
              const newScale = Math.max(0.1, oldScale / 1.2)
              stage.scale({ x: newScale, y: newScale })
            }
          }}
          className="px-3 py-1 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 text-sm"
        >
          -
        </button>
        <button
          onClick={() => {
            const stage = stageRef.current
            if (stage) {
              stage.scale({ x: 1, y: 1 })
              stage.position({ x: stageSize.width / 2, y: stageSize.height / 2 })
              setFitToOutline(false);
            }
          }}
          className="px-2 py-1 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 text-xs"
        >
          Reset
        </button>
        <button
          onClick={() => {
            // Only proceed if we have a valid outline and stage
            if (drawnThreads.length === 0 || !stageRef.current) return;
            
            const stage = stageRef.current;
            
            // Calculate the scale needed to fit the outline to the viewport
            const scaleX = (stageSize.width * 0.8) / boxDimensions.width;
            const scaleY = (stageSize.height * 0.8) / boxDimensions.height;
            const scale = Math.min(scaleX, scaleY);
            
            // Apply the scale
            stage.scale({ x: scale, y: scale });
            
            // Position the stage to center the outline
            const centerX = boxDimensions.x + boxDimensions.width / 2;
            const centerY = boxDimensions.y + boxDimensions.height / 2;
            
            stage.position({
              x: stageSize.width / 2 - centerX * scale,
              y: stageSize.height / 2 - centerY * scale
            });
            
            // Enable fit-to-outline mode (for clipping)
            setFitToOutline(true);
          }}
          className="px-2 py-1 bg-white border border-gray-300 rounded shadow hover:bg-gray-50 text-xs text-center"
          disabled={drawnThreads.length === 0}
        >
          Fit to Design
        </button>
      </div>

      {/* Canvas */}
      <Stage
        ref={stageRef}
        width={stageSize.width}
        height={stageSize.height}
        onClick={handleCanvasClick}
        draggable={!isDrawingMode}
        x={stageSize.width / 2}
        y={stageSize.height / 2}
        onWheel={(e) => {
          e.evt.preventDefault()
          const stage = e.target.getStage()
          const oldScale = stage.scaleX()
          const pointer = stage.getPointerPosition()

          const mousePointTo = {
            x: (pointer.x - stage.x()) / oldScale,
            y: (pointer.y - stage.y()) / oldScale,
          }

          const scaleBy = 1.1
          const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy
          const clampedScale = Math.max(0.1, Math.min(5, newScale))

          stage.scale({ x: clampedScale, y: clampedScale })

          const newPos = {
            x: pointer.x - mousePointTo.x * clampedScale,
            y: pointer.y - mousePointTo.y * clampedScale,
          }
          stage.position(newPos)
        }}
      >
        <Layer>
          {/* Grid Lines */}
          <Group>
            {generateGridLines()}
          </Group>

          {/* Center Axes */}
          <Group>
            {generateAxes()}
          </Group>

          {/* Drawn Threads */}
          <Group>
            {drawnThreads.map((thread) => {
              // If we're in fit-to-outline mode, check if this thread is within the outline
              if (fitToOutline) {
                if (thread.type === 'Warp') {
                  const x = thread.points[0];
                  if (x < boxDimensions.x || x > boxDimensions.x + boxDimensions.width) {
                    return null; // Skip threads outside the outline
                  }
                } else if (thread.type === 'Weft') {
                  const y = thread.points[1];
                  if (y < boxDimensions.y || y > boxDimensions.y + boxDimensions.height) {
                    return null; // Skip threads outside the outline
                  }
                }
              }
              
              return (
                <Line
                  key={thread.id}
                  points={thread.points}
                  stroke={thread.color}
                  strokeWidth={thread.strokeWidth}
                  lineCap="round"
                  lineJoin="round"
                />
              );
            })}
          </Group>
          
          {/* Black rectangular box around weft and warp intersection */}
          {drawnThreads.length > 0 && (
            <Rect
              x={boxDimensions.x}
              y={boxDimensions.y}
              width={boxDimensions.width}
              height={boxDimensions.height}
              stroke="#000000"
              strokeWidth={4}
              fill="transparent"
            />
          )}
        </Layer>
      </Stage>


    </div>
  )
}

export default DesignCanvas
