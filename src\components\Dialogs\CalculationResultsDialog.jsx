import React from 'react'

const CalculationResultsDialog = ({ 
  showResults, 
  setShowResults, 
  calculationResults,
  designNumber 
}) => {
  if (!showResults || !calculationResults) return null

  const { warp, weft, totals } = calculationResults

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-300">
          <div>
            <h2 className="text-xl font-bold text-gray-800">Calculation Results</h2>
            <p className="text-sm text-gray-600">Design: {designNumber || 'Current Design'}</p>
          </div>
          <button
            onClick={() => setShowResults(false)}
            className="text-gray-500 hover:text-gray-700 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded hover:bg-gray-100"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {/* Summary Section */}
          <div className="bg-teal-600 text-white p-4 rounded-lg mb-6">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{totals.warpYarn}</div>
                <div className="text-sm">Total Warp Yarn Required</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{totals.weftYarn}</div>
                <div className="text-sm">Total Weft Yarn Required</div>
              </div>
              <div>
                <div className="text-2xl font-bold">{totals.totalYarn}</div>
                <div className="text-sm">Total Yarn Required</div>
              </div>
            </div>
          </div>

          {/* Warp Yarn Required Section */}
          <div className="mb-6">
            <div className="bg-blue-600 text-white p-3 rounded-t-lg">
              <h3 className="font-bold text-lg">WARP YARN REQUIRED</h3>
              <div className="text-sm">WEAVING TYPE: AUTO LOOM</div>
            </div>
            
            <div className="border border-gray-300 rounded-b-lg overflow-hidden">
              <div className="grid grid-cols-7 gap-0 bg-gray-100 border-b border-gray-300 text-xs font-medium">
                <div className="px-2 py-2 text-center border-r border-gray-300">WIDTH</div>
                <div className="px-2 py-2 text-center border-r border-gray-300">COUNT</div>
                <div className="px-2 py-2 text-center border-r border-gray-300">COLOUR</div>
                <div className="px-2 py-2 text-center border-r border-gray-300">BASE WARP</div>
                <div className="px-2 py-2 text-center border-r border-gray-300">THREADS</div>
                <div className="px-2 py-2 text-center border-r border-gray-300">YARN REQUIRED</div>
                <div className="px-2 py-2 text-center">TOTAL</div>
              </div>
              
              {warp.map((thread, index) => (
                <div key={index} className="grid grid-cols-7 gap-0 border-b border-gray-200 hover:bg-gray-50 text-xs">
                  <div className="px-2 py-2 text-center border-r border-gray-200">61.0"</div>
                  <div className="px-2 py-2 text-center border-r border-gray-200">{thread.count}</div>
                  <div className="px-2 py-2 text-center border-r border-gray-200 flex items-center justify-center space-x-1">
                    <div 
                      className="w-3 h-3 rounded border border-gray-300" 
                      style={{ backgroundColor: thread.colorHex }}
                    ></div>
                    <span>{thread.colour}</span>
                  </div>
                  <div className="px-2 py-2 text-center border-r border-gray-200 font-semibold">{thread.calculations.totalLeaf}</div>
                  <div className="px-2 py-2 text-center border-r border-gray-200 font-semibold">{thread.ends}</div>
                  <div className="px-2 py-2 text-center border-r border-gray-200 font-semibold">{thread.calculations.withPercentage}</div>
                  <div className="px-2 py-2 text-center font-bold">{thread.yarnRequired.toFixed(2)}</div>
                </div>
              ))}
              
              {/* Warp Total Row */}
              <div className="grid grid-cols-7 gap-0 bg-blue-50 border-t-2 border-blue-300 text-xs font-bold">
                <div className="px-2 py-2 text-center border-r border-gray-200" colSpan="6">TOTAL WARP YARN:</div>
                <div className="px-2 py-2 text-center border-r border-gray-200"></div>
                <div className="px-2 py-2 text-center border-r border-gray-200"></div>
                <div className="px-2 py-2 text-center border-r border-gray-200"></div>
                <div className="px-2 py-2 text-center border-r border-gray-200"></div>
                <div className="px-2 py-2 text-center border-r border-gray-200"></div>
                <div className="px-2 py-2 text-center text-blue-800">{totals.warpYarn}</div>
              </div>
            </div>
          </div>

          {/* Weft Yarn Required Section */}
          <div className="mb-6">
            <div className="bg-green-600 text-white p-3 rounded-t-lg">
              <h3 className="font-bold text-lg">WEFT YARN REQUIRED</h3>
              <div className="text-sm">THREADS</div>
            </div>
            
            <div className="border border-gray-300 rounded-b-lg overflow-hidden">
              <div className="grid grid-cols-4 gap-0 bg-gray-100 border-b border-gray-300 text-xs font-medium">
                <div className="px-2 py-2 text-center border-r border-gray-300">COUNT</div>
                <div className="px-2 py-2 text-center border-r border-gray-300">COLOUR</div>
                <div className="px-2 py-2 text-center border-r border-gray-300">PICKS</div>
                <div className="px-2 py-2 text-center">YARN REQUIRED</div>
              </div>
              
              {weft.map((thread, index) => (
                <div key={index} className="grid grid-cols-4 gap-0 border-b border-gray-200 hover:bg-gray-50 text-xs">
                  <div className="px-2 py-2 text-center border-r border-gray-200">{thread.count}</div>
                  <div className="px-2 py-2 text-center border-r border-gray-200 flex items-center justify-center space-x-1">
                    <div 
                      className="w-3 h-3 rounded border border-gray-300" 
                      style={{ backgroundColor: thread.colorHex }}
                    ></div>
                    <span>{thread.colour}</span>
                  </div>
                  <div className="px-2 py-2 text-center border-r border-gray-200 font-semibold">{thread.ends}</div>
                  <div className="px-2 py-2 text-center font-bold">{thread.yarnRequired.toFixed(2)}</div>
                </div>
              ))}
              
              {/* Weft Total Row */}
              <div className="grid grid-cols-4 gap-0 bg-green-50 border-t-2 border-green-300 text-xs font-bold">
                <div className="px-2 py-2 text-center border-r border-gray-200" colSpan="3">TOTAL WEFT YARN:</div>
                <div className="px-2 py-2 text-center border-r border-gray-200"></div>
                <div className="px-2 py-2 text-center border-r border-gray-200"></div>
                <div className="px-2 py-2 text-center text-green-800">{totals.weftYarn}</div>
              </div>
            </div>
          </div>

          {/* Calculation Notes */}
          <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
            <h4 className="font-semibold text-yellow-800 mb-2">Calculation Notes:</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• 5% crimp added for dyeing process</li>
              <li>• 7% crimp added for wrapping process</li>
              <li>• Cone calculation: 1 bundle = 4.54 kgs, const value = 7685</li>
              <li>• Warp calculation: read × agalam = total leaf</li>
              <li>• Excess percentage formula: a = a + (x×a/100)</li>
            </ul>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-300 px-6 py-3 flex justify-between">
          <button
            onClick={() => window.print()}
            className="px-6 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Print Results
          </button>
          <button
            onClick={() => setShowResults(false)}
            className="px-6 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

export default CalculationResultsDialog
