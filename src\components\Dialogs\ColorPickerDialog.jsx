import React, { useState, useEffect } from 'react'

const ColorPickerDialog = ({
  showColorPicker,
  setShowColorPicker,
  selectedColorName,
  customColor,
  setCustomColor,
  numberOfThreads,
  setNumberOfThreads,
  setSelectedColor,
  setSelectedColorName,
  setIsDrawingMode,
  colorNames,
  addToFavorites,
  addToRecentColors
}) => {
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)
  const [colorName, setColorName] = useState('')
  const [rgbValues, setRgbValues] = useState({ r: 0, g: 0, b: 0 })
  
  // Convert hex to RGB when customColor changes
  useEffect(() => {
    const hex = customColor.replace('#', '')
    const r = parseInt(hex.substring(0, 2), 16)
    const g = parseInt(hex.substring(2, 4), 16)
    const b = parseInt(hex.substring(4, 6), 16)
    setRgbValues({ r, g, b })
  }, [customColor])
  
  // Update hex color when RGB values change
  const updateFromRgb = (component, value) => {
    const newRgb = { ...rgbValues, [component]: parseInt(value) }
    setRgbValues(newRgb)
    const hexColor = '#' + 
      newRgb.r.toString(16).padStart(2, '0') + 
      newRgb.g.toString(16).padStart(2, '0') + 
      newRgb.b.toString(16).padStart(2, '0')
    setCustomColor(hexColor)
  }

  const handleOkClick = () => {
    setSelectedColor(customColor)
    const finalColorName = colorNames[customColor] || 'CUSTOM'
    setSelectedColorName(finalColorName)
    setShowColorPicker(false)
    setIsDrawingMode(true)
    addToRecentColors(customColor)
  }

  const handleCancelClick = () => {
    setShowColorPicker(false)
  }

  const handleAddToFavorites = () => {
    addToFavorites(customColor, colorName || null)
  }

  if (!showColorPicker) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-[450px]">
        <h3 className="text-lg font-semibold mb-4">Thread Information</h3>
        
        <div className="space-y-4">
          <div className="flex justify-between">
            <div className="w-1/2 pr-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Selected Color: {selectedColorName}
              </label>
              <div className="mb-2">
                <div 
                  className="w-full h-12 rounded border border-gray-300" 
                  style={{ backgroundColor: customColor }}
                ></div>
              </div>
              <div className="flex items-center space-x-2 mb-4">
                <input
                  type="color"
                  value={customColor}
                  onChange={(e) => setCustomColor(e.target.value)}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <div>
                  <label className="block text-xs text-gray-500">Hex</label>
                  <input 
                    type="text"
                    value={customColor}
                    onChange={(e) => setCustomColor(e.target.value)}
                    className="border border-gray-300 rounded p-1 text-sm w-24"
                  />
                </div>
              </div>

              <button
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                className="text-xs text-blue-600 hover:underline"
              >
                {showAdvancedOptions ? 'Hide Advanced' : 'Show Advanced Options'}
              </button>
            </div>

            <div className="w-1/2 pl-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Save as Favorite
              </label>
              <div className="flex flex-col space-y-2">
                <input
                  type="text"
                  placeholder="Color name (optional)"
                  value={colorName}
                  onChange={(e) => setColorName(e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleAddToFavorites}
                  className="w-full py-2 text-sm text-gray-600 bg-gray-200 rounded hover:bg-gray-300 transition-colors flex items-center justify-center"
                  title="Add to Favorites"
                >
                  <span className="mr-1">★</span> Save to Favorites
                </button>
              </div>
            </div>
          </div>

          {showAdvancedOptions && (
            <div className="border-t border-gray-200 pt-3">
              <h4 className="text-sm font-medium text-gray-700 mb-2">RGB Controls</h4>
              <div className="space-y-2">
                <div>
                  <div className="flex items-center">
                    <label className="text-xs font-medium text-red-500 w-6">R</label>
                    <input
                      type="range"
                      min="0"
                      max="255"
                      value={rgbValues.r}
                      onChange={(e) => updateFromRgb('r', e.target.value)}
                      className="w-full h-2 bg-red-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <input
                      type="number"
                      min="0"
                      max="255"
                      value={rgbValues.r}
                      onChange={(e) => updateFromRgb('r', e.target.value)}
                      className="w-12 text-xs p-1 border border-gray-300 rounded ml-2"
                    />
                  </div>
                </div>
                <div>
                  <div className="flex items-center">
                    <label className="text-xs font-medium text-green-500 w-6">G</label>
                    <input
                      type="range"
                      min="0"
                      max="255"
                      value={rgbValues.g}
                      onChange={(e) => updateFromRgb('g', e.target.value)}
                      className="w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <input
                      type="number"
                      min="0"
                      max="255"
                      value={rgbValues.g}
                      onChange={(e) => updateFromRgb('g', e.target.value)}
                      className="w-12 text-xs p-1 border border-gray-300 rounded ml-2"
                    />
                  </div>
                </div>
                <div>
                  <div className="flex items-center">
                    <label className="text-xs font-medium text-blue-500 w-6">B</label>
                    <input
                      type="range"
                      min="0"
                      max="255"
                      value={rgbValues.b}
                      onChange={(e) => updateFromRgb('b', e.target.value)}
                      className="w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <input
                      type="number"
                      min="0"
                      max="255"
                      value={rgbValues.b}
                      onChange={(e) => updateFromRgb('b', e.target.value)}
                      className="w-12 text-xs p-1 border border-gray-300 rounded ml-2"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="border-t border-gray-200 pt-3">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Number of threads (ends):
            </label>
            <input
              type="number"
              value={numberOfThreads}
              onChange={(e) => setNumberOfThreads(parseInt(e.target.value) || 1)}
              min="1"
              max="50"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Thread count:
            </label>
            <div className="text-sm text-gray-600">60 by 2</div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Color Information:
            </label>
            <div className="text-sm text-gray-600 space-y-1">
              <div>Hex: {customColor}</div>
              <div>Name: {colorName || selectedColorName}</div>
              <div>Threads: {numberOfThreads}</div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={handleCancelClick}
            className="px-4 py-2 text-sm text-gray-600 bg-gray-200 rounded hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleOkClick}
            className="px-4 py-2 text-sm text-white bg-blue-600 rounded hover:bg-blue-700 transition-colors"
          >
            OK - Start Drawing
          </button>
        </div>
      </div>
    </div>
  )
}

export default ColorPickerDialog
