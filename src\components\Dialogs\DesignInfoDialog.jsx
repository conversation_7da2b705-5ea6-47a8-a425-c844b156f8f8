import React from 'react'

const DesignInfoDialog = ({
  showDesignInfo,
  setShowDesignInfo,
  designNumber,
  setDesignNumber,
  designName,
  setDesignName,
  handleDesignInfoSubmit,
  handleDesignInfoCancel
}) => {
  if (!showDesignInfo) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-96">
        <h3 className="text-lg font-semibold mb-4">Design Information</h3>
        
        <div className="mb-4 text-sm text-gray-600">
          Please enter the design information before proceeding with color selection.
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Design Number:
            </label>
            <input
              type="text"
              value={designNumber}
              onChange={(e) => setDesignNumber(e.target.value)}
              placeholder="e.g., 123, ABC/456"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Design Name:
            </label>
            <input
              type="text"
              value={designName}
              onChange={(e) => setDesignName(e.target.value)}
              placeholder="e.g., Summer Collection 2023"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded p-3">
            <div className="text-sm text-blue-800">
              <strong>Note:</strong> This information will be used in the Design Specification document 
              and cannot be changed later without creating a new design.
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={handleDesignInfoCancel}
            className="px-4 py-2 text-sm text-gray-600 bg-gray-200 rounded hover:bg-gray-300 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleDesignInfoSubmit}
            disabled={!designNumber.trim() || !designName.trim()}
            className="px-4 py-2 text-sm text-white bg-blue-600 rounded hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  )
}

export default DesignInfoDialog
