import React, { useState, useEffect, useRef } from 'react'
import DesignPrintTemplate from '../Print/DesignPrintTemplate'
import CalculationResultsDialog from './CalculationResultsDialog'
import { calculateIndividualThreads, formatCalculationResults } from '../../utils/textileCalculations'

const DesignSpecificationDialog = ({ 
  showDesignSpecification, 
  setShowDesignSpecification,
  designSpecificationTab,
  setDesignSpecificationTab,
  drawnThreads,
  designNumber,
  designName,
  setDrawnThreads,
  saveDesign,
  loadDesign
}) => {
  // Design Specification form states
  const [reedValue, setReedValue] = useState('45')
  const [picksValue, setPicksValue] = useState('0')
  const [widWrpValue, setWidWrpValue] = useState('20.00')
  const [wftValue, setWftValue] = useState('20.00')
  const [warpRepeats, setWarpRepeats] = useState('')
  const [weftRepeats, setWeftRepeats] = useState('')

  // Calculation form states
  const [calculationLength, setCalculationLength] = useState('1500.00')
  const [calculationLess, setCalculationLess] = useState('0.00')
  const [calculationWarpPercent, setCalculationWarpPercent] = useState('20.00')
  const [calculationWeftPercent, setCalculationWeftPercent] = useState('6.00')
  const [calculationRead, setCalculationRead] = useState('')
  const [calculationAgalam, setCalculationAgalam] = useState('')
  const [showCalculationResults, setShowCalculationResults] = useState(false)
  const [calculationResults, setCalculationResults] = useState(null)
  
  // State for saved designs
  const [savedDesigns, setSavedDesigns] = useState([])
  const [currentDesignNo, setCurrentDesignNo] = useState(designNumber || '')
  const [currentDesignName, setCurrentDesignName] = useState(designName || '')
  
  // Reference for print iframe
  const printFrameRef = useRef(null)

  // Helper functions for input validation and formatting
  const handleNumberInput = (value, setter, isDecimal = false) => {
    if (isDecimal) {
      // Allow decimal numbers
      const regex = /^\d*\.?\d*$/
      if (regex.test(value) || value === '') {
        setter(value)
      }
    } else {
      // Allow only integers
      const regex = /^\d*$/
      if (regex.test(value) || value === '') {
        setter(value)
      }
    }
  }

  const formatDecimalValue = (value) => {
    if (value === '' || isNaN(parseFloat(value))) return value
    return parseFloat(value).toFixed(2)
  }

  // Get current thread counts from drawn threads
  const getCurrentThreadCounts = () => {
    const analysis = analyzeThreads()
    return {
      warpThreads: analysis.warp.reduce((sum, row) => sum + row.ends, 0),
      weftThreads: analysis.weft.reduce((sum, row) => sum + row.ends, 0)
    }
  }

  // Calculate total threads
  const calculateTotalWarpThreads = () => {
    const threads = getCurrentThreadCounts().warpThreads
    const repeats = parseInt(warpRepeats) || 0
    return threads * repeats
  }

  const calculateTotalWeftThreads = () => {
    const threads = getCurrentThreadCounts().weftThreads
    const repeats = parseInt(weftRepeats) || 0
    return threads * repeats
  }

  const calculateOverallThreadCount = () => {
    return calculateTotalWarpThreads() + calculateTotalWeftThreads()
  }

  // Handle calculation execution
  const handleCalculateDesign = () => {
    const threadAnalysis = analyzeThreads()

    if (threadAnalysis.warp.length === 0 && threadAnalysis.weft.length === 0) {
      alert('Please draw some threads first before calculating.')
      return
    }

    if (!calculationRead || !calculationAgalam) {
      alert('Please enter Read and Agalam values for calculation.')
      return
    }

    const calculationParams = {
      length: parseFloat(calculationLength) || 1500,
      warpPercentage: parseFloat(calculationWarpPercent) || 20,
      weftPercentage: parseFloat(calculationWeftPercent) || 6,
      read: parseFloat(calculationRead) || 0,
      agalam: parseFloat(calculationAgalam) || 0,
      lessPercentage: parseFloat(calculationLess) || 0
    }

    const calculations = calculateIndividualThreads(threadAnalysis, calculationParams)
    const formattedResults = formatCalculationResults(calculations)

    setCalculationResults(formattedResults)
    setShowCalculationResults(true)
  }

  // Reset design specification form
  const resetDesignSpecification = () => {
    setReedValue('45')
    setPicksValue('0')
    setWidWrpValue('20.00')
    setWftValue('20.00')
    setWarpRepeats('')
    setWeftRepeats('')
  }

  // Analyze drawn threads and create data for WARP and WEFT tables
  const analyzeThreads = () => {
    // For warp threads (vertical), we need unique x-positions
    // For weft threads (horizontal), we need unique y-positions
    
    // Create maps to track the topmost thread at each position
    const warpPositionMap = new Map();
    const weftPositionMap = new Map();
    
    // Process all threads to find the topmost thread at each position
    [...drawnThreads].reverse().forEach(thread => {
      if (thread.type === 'Warp') {
        // For warp threads, use x position as key
        const xPos = thread.points[0];
        if (!warpPositionMap.has(xPos)) {
          warpPositionMap.set(xPos, thread);
        }
      } else {
        // For weft threads, use y position as key
        const yPos = thread.points[1];
        if (!weftPositionMap.has(yPos)) {
          weftPositionMap.set(yPos, thread);
        }
      }
    });
    
    // Get unique threads sorted by position
    const warpThreads = Array.from(warpPositionMap.values())
      .sort((a, b) => a.points[0] - b.points[0]);
    
    const weftThreads = Array.from(weftPositionMap.values())
      .sort((a, b) => a.points[1] - b.points[1]);

    const analyzeByType = (threads) => {
      // Track consecutive threads of the same color
      let sequentialThreads = []
      let currentColor = null
      let currentCount = 0
      
      // Process threads in order of their position on canvas
      threads.forEach(thread => {
        if (currentColor === null) {
          // First thread
          currentColor = thread.color
          currentCount = 1
        } else if (currentColor === thread.color) {
          // Same color as previous, increment count
          currentCount++
        } else {
          // New color, push previous group and start new one
          sequentialThreads.push({
            color: currentColor,
            colorName: threads.find(t => t.color === currentColor).colorName,
            count: currentCount
          })
          currentColor = thread.color
          currentCount = 1
        }
      })
      
      // Add the last group if there are any threads
      if (currentColor !== null) {
        sequentialThreads.push({
          color: currentColor,
          colorName: threads.find(t => t.color === currentColor).colorName,
          count: currentCount
        })
      }

      return sequentialThreads.map((group, index) => ({
        group: index + 1,
        count: '60/2', // Dummy thread thickness
        colour: group.colorName,
        colorHex: group.color,
        ends: group.count,
        set: 1,
        seer: 'N'
      }))
    }

    return {
      warp: analyzeByType(warpThreads),
      weft: analyzeByType(weftThreads)
    }
  }

  // Load saved designs when dialog opens
  useEffect(() => {
    if (showDesignSpecification) {
      // Set current design info
      setCurrentDesignNo(designNumber || '')
      setCurrentDesignName(designName || '')
      
      // Load saved designs from localStorage
      const storedDesigns = localStorage.getItem('savedDesigns')
      if (storedDesigns) {
        setSavedDesigns(JSON.parse(storedDesigns))
      }
    }
  }, [showDesignSpecification, designNumber, designName])

  // Function to handle saving the current design
  const handleSaveDesign = () => {
    if (!currentDesignNo || !currentDesignName) {
      alert('Please enter both Design Number and Design Name')
      return
    }
    
    // Create design data object
    const designData = {
      id: Date.now().toString(),
      designNumber: currentDesignNo,
      designName: currentDesignName,
      reedValue,
      picksValue,
      widWrpValue,
      wftValue,
      drawnThreads,
      timestamp: new Date().toISOString()
    }
    
    // Update saved designs
    const existingDesignIndex = savedDesigns.findIndex(
      design => design.designNumber === currentDesignNo
    )
    
    let updatedDesigns
    if (existingDesignIndex >= 0) {
      // Update existing design
      updatedDesigns = [...savedDesigns]
      updatedDesigns[existingDesignIndex] = designData
    } else {
      // Add new design
      updatedDesigns = [...savedDesigns, designData]
    }
    
    // Save to localStorage
    localStorage.setItem('savedDesigns', JSON.stringify(updatedDesigns))
    setSavedDesigns(updatedDesigns)
    
    // Call the parent save function if provided
    if (saveDesign) {
      saveDesign(currentDesignNo, currentDesignName, drawnThreads)
    }
    
    alert(`Design ${currentDesignNo} saved successfully`)
  }
  
  // Function to handle loading a design
  const handleLoadDesign = (design) => {
    if (loadDesign) {
      loadDesign(design.designNumber, design.designName, design.drawnThreads)
      setCurrentDesignNo(design.designNumber)
      setCurrentDesignName(design.designName)
      setReedValue(design.reedValue || '45')
      setPicksValue(design.picksValue || '0')
      setWidWrpValue(design.widWrpValue || '20.00')
      setWftValue(design.wftValue || '20.00')
      setDesignSpecificationTab('Specification Entry')
    }
  }
  
  // Function to print design specification
  const handlePrintDesign = (design) => {
    // Create a print-specific window
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    
    if (!printWindow) {
      alert('Please allow pop-ups to enable printing');
      return;
    }
    
    // Create the thread analysis
    const threadAnalysis = {
      warp: [],
      weft: []
    };
    
    // Group threads by type, color and count
    const threadGroups = {};
    
    design.drawnThreads.forEach(thread => {
      const key = `${thread.type}-${thread.color}-${thread.count}`;
      if (!threadGroups[key]) {
        threadGroups[key] = {
          type: thread.type,
          colour: thread.colorName || 'UNKNOWN',
          count: thread.count || (thread.type === 'Warp' ? '30sf' : '2/40'),
          threads: []
        };
      }
      threadGroups[key].threads.push(thread);
    });
    
    // Convert thread groups to warp and weft arrays
    Object.values(threadGroups).forEach(group => {
      const entry = {
        colour: group.colour.toUpperCase(),
        count: group.count,
        ends: group.threads.length,
        set: '18' // Default value, could be made dynamic
      };
      
      if (group.type === 'Warp') {
        threadAnalysis.warp.push(entry);
      } else {
        threadAnalysis.weft.push(entry);
      }
    });
    
    // Calculate totals
    const totalWarpEnds = threadAnalysis.warp.reduce((total, w) => total + w.ends, 0);
    const totalWeftEnds = threadAnalysis.weft.reduce((total, w) => total + w.ends, 0);
    const warpRepeats = 119; // Fixed value for demonstration
    const totalThreads = totalWarpEnds * warpRepeats;
    
    // Pre-calculate dots for color names
    const getDots = (count) => {
      let dots = '';
      for (let i = 0; i < count; i++) {
        dots += '.';
      }
      return dots;
    };
    const dots20 = getDots(20);
    
    // Generate the print HTML
    const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Design Specification - ${design.designNumber}</title>
        <style>
          body {
            font-family: 'Courier New', monospace;
            padding: 20px;
            font-size: 12px;
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
          }
          .header h1 {
            font-size: 18px;
            margin: 0;
          }
          .header h2 {
            font-size: 16px;
            margin: 10px 0 0 0;
          }
          .design-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
          }
          .info-section {
            width: 48%;
          }
          .info-row {
            margin-bottom: 5px;
          }
          .label {
            display: inline-block;
            width: 120px;
          }
          .value {
            font-weight: bold;
          }
          .page-number {
            position: absolute;
            right: 20px;
            top: 50px;
          }
          .section-title {
            font-weight: bold;
            margin: 15px 0 5px 0;
          }
          .thread-table {
            width: 100%;
            margin-bottom: 10px;
          }
          .thread-table th {
            text-align: left;
            font-weight: bold;
          }
          .thread-table td {
            padding: 2px 0;
          }
          @media print {
            body {
              margin: 0;
              padding: 15px;
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>THILAGA IMPEX</h1>
          <h2>DESIGN SPECIFICATION</h2>
        </div>
        
        <div class="page-number">PAGE: 1</div>
        
        <div class="design-info">
          <div class="info-section">
            <div class="info-row">
              <span class="label">DESIGN NO. :</span>
              <span class="value">${design.designNumber}</span>
            </div>
            <div class="info-row">
              <span class="label">REED :</span>
              <span class="value">${design.reedValue || '68'}</span>
            </div>
            <div class="info-row">
              <span class="label">PICKS :</span>
              <span class="value">${design.picksValue || '68'}</span>
            </div>
            <div class="info-row">
              <span class="label">WIDTH :</span>
              <span class="value">${design.widWrpValue || '63.0'}</span>
            </div>
          </div>
          
          <div class="info-section">
            <div class="info-row">
              <span class="label">COLOUR :</span>
              <span class="value">${threadAnalysis.warp.map(w => w.colour).join(', ')}</span>
            </div>
            <div class="info-row">
              <span class="label">WARP COUNT :</span>
              <span class="value">${threadAnalysis.warp.map(w => w.count).filter((c, i, arr) => arr.indexOf(c) === i).join(', ')}</span>
            </div>
            <div class="info-row">
              <span class="label">WEFT COUNT :</span>
              <span class="value">${threadAnalysis.weft.map(w => w.count).filter((c, i, arr) => arr.indexOf(c) === i).join(', ')}</span>
            </div>
            <div class="info-row">
              <span class="label">WEAVE TYPE :</span>
              <span class="value">seer sucker</span>
            </div>
          </div>
        </div>
        
        <div class="section-title">BASE WARP</div>
        <table class="thread-table">
          <tr>
            <th style="width: 15%">COUNT</th>
            <th style="width: 55%">COLOUR</th>
            <th style="width: 15%">ENDS</th>
            <th style="width: 15%">SETS</th>
          </tr>
          ${threadAnalysis.warp.map((item, index) => `
            <tr>
              <td>${index + 1} ${item.count}</td>
              <td>${item.colour}${dots20}</td>
              <td>${item.ends}</td>
              <td>${item.set}</td>
            </tr>
          `).join('')}
        </table>
        
        <div>
          <div class="info-row">
            <span class="label">TOTAL</span>
            <span class="value">${totalWarpEnds} X${warpRepeats}=${totalThreads}</span>
          </div>
          <div class="info-row">
            <span class="label">EXTRA THREADS</span>
            <span class="value">0</span>
          </div>
          <div class="info-row">
            <span class="label">TOTAL THREADS</span>
            <span class="value">${totalThreads}</span>
          </div>
        </div>
        
        <div class="section-title" style="margin-top: 20px">WEFT</div>
        <table class="thread-table">
          <tr>
            <th style="width: 15%">COUNT</th>
            <th style="width: 55%">COLOUR</th>
            <th style="width: 15%">ENDS</th>
            <th style="width: 15%">SETS</th>
          </tr>
          ${threadAnalysis.weft.map((item, index) => `
            <tr>
              <td>${index + 1} ${item.count}</td>
              <td>${item.colour}${dots20}</td>
              <td>${item.ends}</td>
              <td>${item.set}</td>
            </tr>
          `).join('')}
        </table>
        
        <div class="info-row">
          <span class="label">TOTAL THREADS</span>
          <span class="value">${totalWeftEnds}</span>
        </div>
      </body>
      </html>
    `;
    
    // Write to the new window and print
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();
    
    // Print when resources and images have loaded
    printWindow.onload = function() {
      setTimeout(() => {
        printWindow.print();
        printWindow.onafterprint = function() {
          printWindow.close();
        };
      }, 500);
    };
  }
  
  if (!showDesignSpecification) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-5/6 h-5/6 flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-300 px-6 py-3 flex justify-between items-center">
          <h2 className="text-lg font-semibold">Design Specification</h2>
          <button
            onClick={() => setShowDesignSpecification(false)}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-300 px-6">
          <div className="flex space-x-4">
            <button
              onClick={() => setDesignSpecificationTab('Design Selection')}
              className={`py-2 px-4 text-sm font-medium border-b-2 ${
                designSpecificationTab === 'Design Selection'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Design Selection
            </button>
            <button
              onClick={() => setDesignSpecificationTab('Specification Entry')}
              className={`py-2 px-4 text-sm font-medium border-b-2 ${
                designSpecificationTab === 'Specification Entry'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Specification Entry
            </button>
            <button
              onClick={() => setDesignSpecificationTab('Calculations')}
              className={`py-2 px-4 text-sm font-medium border-b-2 ${
                designSpecificationTab === 'Calculations'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              Calculations
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-6 py-4">
          {designSpecificationTab === 'Design Selection' && (
            <div className="py-4">
              <div className="text-gray-600 mb-6">
                <h3 className="text-lg font-semibold mb-2">Saved Designs</h3>
                <p className="text-sm text-gray-500">Select a design to load, or create a new design in the Specification Entry tab.</p>
              </div>
              
              {savedDesigns.length === 0 ? (
                <div className="text-center py-8 bg-gray-50 rounded border border-gray-200">
                  <p className="text-gray-500">No saved designs found.</p>
                  <p className="text-sm text-gray-400 mt-2">Create and save a design in the Specification Entry tab.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
                  {savedDesigns.map((design) => (
                    <div 
                      key={design.id} 
                      className="border border-gray-200 rounded-lg p-4 hover:bg-blue-50 hover:border-blue-300 transition-colors"
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-semibold text-blue-700">{design.designNumber}</h4>
                          <p className="text-gray-600">{design.designName}</p>
                        </div>
                        <div className="text-xs text-gray-400">
                          {new Date(design.timestamp).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="mt-2 flex justify-between text-sm">
                        <span className="text-gray-500">Warp: <span className="font-medium">{design.drawnThreads.filter(t => t.type === 'Warp').length}</span></span>
                        <span className="text-gray-500">Weft: <span className="font-medium">{design.drawnThreads.filter(t => t.type === 'Weft').length}</span></span>
                      </div>
                      <div className="mt-3 flex justify-end space-x-2">
                        <button
                          onClick={() => handleLoadDesign(design)}
                          className="px-4 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors"
                        >
                          Load Design
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePrintDesign(design);
                          }}
                          className="px-4 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                          </svg>
                          Print
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {designSpecificationTab === 'Specification Entry' && (
            <div>
              {/* First Row - Only Design Number and Name */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">DESIGN NO:</label>
                  <input 
                    type="text" 
                    value={currentDesignNo}
                    onChange={(e) => setCurrentDesignNo(e.target.value)}
                    className="w-full px-3 py-1 border border-gray-300 rounded text-sm" 
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">DESIGN NAME:</label>
                  <input 
                    type="text" 
                    value={currentDesignName}
                    onChange={(e) => setCurrentDesignName(e.target.value)}
                    className="w-full px-3 py-1 border border-gray-300 rounded text-sm" 
                  />
                </div>
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-6 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">WEAVING TYPE:</label>
                  <select className="w-full px-3 py-1 border border-gray-300 rounded text-sm">
                    <option>AUTO LOOM</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">REED:</label>
                  <input 
                    type="text" 
                    value={reedValue}
                    onChange={(e) => handleNumberInput(e.target.value, setReedValue, false)}
                    onBlur={(e) => {
                      if (e.target.value === '') setReedValue('0')
                    }}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm text-center bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300 focus:bg-purple-400" 
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">PICKS:</label>
                  <input 
                    type="text" 
                    value={picksValue}
                    onChange={(e) => handleNumberInput(e.target.value, setPicksValue, false)}
                    onBlur={(e) => {
                      if (e.target.value === '') setPicksValue('0')
                    }}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm text-center bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300 focus:bg-purple-400" 
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">WID:WRP</label>
                  <input 
                    type="text" 
                    value={widWrpValue}
                    onChange={(e) => handleNumberInput(e.target.value, setWidWrpValue, true)}
                    onBlur={(e) => {
                      if (e.target.value === '') {
                        setWidWrpValue('0.00')
                      } else {
                        setWidWrpValue(formatDecimalValue(e.target.value))
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm text-center bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300 focus:bg-purple-400" 
                    placeholder="0.00"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">WFT</label>
                  <input 
                    type="text" 
                    value={wftValue}
                    onChange={(e) => handleNumberInput(e.target.value, setWftValue, true)}
                    onBlur={(e) => {
                      if (e.target.value === '') {
                        setWftValue('0.00')
                      } else {
                        setWftValue(formatDecimalValue(e.target.value))
                      }
                    }}
                    className="w-full px-2 py-1 border border-gray-300 rounded text-sm text-center bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300 focus:bg-purple-400" 
                    placeholder="0.00"
                  />
                </div>
                <div className="flex items-center space-x-2 mt-6">
                  <span className="text-sm font-medium text-gray-700">DOBBY:</span>
                  <button className="px-2 py-1 text-xs border border-gray-300 rounded bg-gray-100 hover:bg-gray-200">X</button>
                  <button 
                    onClick={resetDesignSpecification}
                    className="px-3 py-1 text-xs bg-blue-500 text-white border border-blue-500 rounded hover:bg-blue-600"
                    title="Reset all values to defaults"
                  >
                    RESET
                  </button>
                </div>
              </div>

              {/* Warp and Weft Tables */}
              <div className="grid grid-cols-2 gap-6 mb-6">
                {/* Warp Section */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-2">WARP</h3>
                  <div className="border border-gray-300 rounded">
                    <div className="grid grid-cols-6 gap-0 bg-gray-100 border-b border-gray-300">
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">GROUP</div>
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">COUNT</div>
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">COLOUR</div>
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">ENDS</div>
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">SET</div>
                      <div className="px-2 py-1 text-xs font-medium text-center">SEER</div>
                    </div>
                    <div className="max-h-40 min-h-16 bg-white overflow-y-auto">
                      {analyzeThreads().warp.length === 0 ? (
                        <div className="flex items-center justify-center h-full text-xs text-gray-500">
                          No warp threads drawn yet
                        </div>
                      ) : (
                        analyzeThreads().warp.map((row, index) => (
                          <div key={index} className="grid grid-cols-6 gap-0 border-b border-gray-200 hover:bg-gray-50">
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200">{row.group}</div>
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200">{row.count}</div>
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200 flex items-center justify-center space-x-1">
                              <div 
                                className="w-3 h-3 rounded border border-gray-300" 
                                style={{ backgroundColor: row.colorHex }}
                                title={row.colorHex}
                              ></div>
                              <span>{row.colour}</span>
                            </div>
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200 font-semibold">{row.ends}</div>
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200">{row.set}</div>
                            <div className="px-2 py-1 text-xs text-center">{row.seer}</div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>

                {/* Weft Section */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-700 mb-2">WEFT</h3>
                  <div className="border border-gray-300 rounded">
                    <div className="grid grid-cols-4 gap-0 bg-gray-100 border-b border-gray-300">
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">COUNT</div>
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">COLOUR</div>
                      <div className="px-2 py-1 text-xs font-medium text-center border-r border-gray-300">ENDS</div>
                      <div className="px-2 py-1 text-xs font-medium text-center">SET</div>
                    </div>
                    <div className="max-h-40 min-h-16 bg-white overflow-y-auto">
                      {analyzeThreads().weft.length === 0 ? (
                        <div className="flex items-center justify-center h-full text-xs text-gray-500">
                          No weft threads drawn yet
                        </div>
                      ) : (
                        analyzeThreads().weft.map((row, index) => (
                          <div key={index} className="grid grid-cols-4 gap-0 border-b border-gray-200 hover:bg-gray-50">
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200">{row.count}</div>
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200 flex items-center justify-center space-x-1">
                              <div 
                                className="w-3 h-3 rounded border border-gray-300" 
                                style={{ backgroundColor: row.colorHex }}
                                title={row.colorHex}
                              ></div>
                              <span>{row.colour}</span>
                            </div>
                            <div className="px-2 py-1 text-xs text-center border-r border-gray-200 font-semibold">{row.ends}</div>
                            <div className="px-2 py-1 text-xs text-center">{row.set}</div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Thread Summary */}
              <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
                <h4 className="text-sm font-semibold text-blue-800 mb-2">Thread Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div>
                    <span className="font-medium text-blue-700">Warp Threads:</span>
                    <span className="ml-2 font-semibold">{getCurrentThreadCounts().warpThreads}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Weft Threads:</span>
                    <span className="ml-2 font-semibold">{getCurrentThreadCounts().weftThreads}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Warp Colors:</span>
                    <span className="ml-2 font-semibold">{analyzeThreads().warp.length}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-700">Weft Colors:</span>
                    <span className="ml-2 font-semibold">{analyzeThreads().weft.length}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {designSpecificationTab === 'Calculations' && (
            <div>
              <div className="bg-purple-50 border border-purple-200 rounded p-4 mb-6">
                <h3 className="text-lg font-semibold text-purple-800 mb-2">DESIGN CALCULATION</h3>
                <p className="text-sm text-purple-600">Enter the calculation parameters for your textile design</p>
              </div>

              {/* Design Info Section */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">DESIGN NO. :</label>
                  <div className="px-3 py-2 bg-gray-100 border border-gray-300 rounded text-sm font-semibold">
                    {currentDesignNo || designNumber || 'Not Set'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">LENGTH :</label>
                  <input
                    type="text"
                    value={calculationLength}
                    onChange={(e) => handleNumberInput(e.target.value, setCalculationLength, true)}
                    className="w-full px-3 py-2 border border-gray-300 rounded text-sm bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300"
                  />
                </div>
              </div>

              {/* Less Percentage */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">LESS :</label>
                  <div className="flex items-center">
                    <input
                      type="text"
                      value={calculationLess}
                      onChange={(e) => handleNumberInput(e.target.value, setCalculationLess, true)}
                      className="w-20 px-3 py-2 border border-gray-300 rounded text-sm bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300"
                    />
                    <span className="ml-2 text-sm font-medium text-gray-700">%</span>
                  </div>
                </div>
              </div>

              {/* Warp and Weft Percentages */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ADD % FOR WARP :</label>
                  <input
                    type="text"
                    value={calculationWarpPercent}
                    onChange={(e) => handleNumberInput(e.target.value, setCalculationWarpPercent, true)}
                    className="w-full px-3 py-2 border border-gray-300 rounded text-sm bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ADD % FOR WEFT :</label>
                  <input
                    type="text"
                    value={calculationWeftPercent}
                    onChange={(e) => handleNumberInput(e.target.value, setCalculationWeftPercent, true)}
                    className="w-full px-3 py-2 border border-gray-300 rounded text-sm bg-purple-500 text-white font-semibold focus:outline-none focus:ring-2 focus:ring-purple-300"
                  />
                </div>
              </div>

              {/* Read and Agalam for Cone Calculation */}
              <div className="bg-blue-50 border border-blue-200 rounded p-4 mb-6">
                <h4 className="text-sm font-semibold text-blue-800 mb-3">Cone Calculation Parameters</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">READ (lines per inch) :</label>
                    <input
                      type="text"
                      value={calculationRead}
                      onChange={(e) => handleNumberInput(e.target.value, setCalculationRead, false)}
                      className="w-full px-3 py-2 border border-blue-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter read value"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-1">AGALAM :</label>
                    <input
                      type="text"
                      value={calculationAgalam}
                      onChange={(e) => handleNumberInput(e.target.value, setCalculationAgalam, false)}
                      className="w-full px-3 py-2 border border-blue-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter agalam value"
                    />
                  </div>
                </div>
                <div className="mt-3 text-xs text-blue-600">
                  <p><strong>Note:</strong> 1 bundle = 4.54 kgs, Const value = 7685</p>
                  <p>Formula: read × agalam = total leaf</p>
                </div>
              </div>

              {/* Calculate Button */}
              <div className="text-center mb-4">
                <button
                  onClick={handleCalculateDesign}
                  className="px-8 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors"
                >
                  CALCULATE
                </button>
              </div>

              {/* Thread Summary for Reference */}
              <div className="bg-gray-50 border border-gray-200 rounded p-4">
                <h4 className="text-sm font-semibold text-gray-800 mb-2">Current Design Summary</h4>
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div>
                    <span className="font-medium text-gray-700">Warp Threads:</span>
                    <span className="ml-2 font-semibold">{getCurrentThreadCounts().warpThreads}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Weft Threads:</span>
                    <span className="ml-2 font-semibold">{getCurrentThreadCounts().weftThreads}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Warp Colors:</span>
                    <span className="ml-2 font-semibold">{analyzeThreads().warp.length}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Weft Colors:</span>
                    <span className="ml-2 font-semibold">{analyzeThreads().weft.length}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Bottom Section - Only show in Specification Entry tab */}
          {designSpecificationTab === 'Specification Entry' && (
            <div>
              {/* Warp Section */}
              <div className="grid grid-cols-3 gap-4 items-center mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Warp Threads:</label>
                  <div className="text-lg font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded border">{getCurrentThreadCounts().warpThreads}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Warp Repeats:</label>
                  <input
                    type="text"
                    value={warpRepeats}
                    onChange={(e) => handleNumberInput(e.target.value, setWarpRepeats, false)}
                    className="w-full px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter warp repeats"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total Warp Threads:</label>
                  <div className="text-lg font-semibold text-blue-600 bg-blue-50 px-2 py-1 rounded border">{calculateTotalWarpThreads()}</div>
                </div>
              </div>

              {/* Weft Section */}
              <div className="grid grid-cols-3 gap-4 items-center mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Weft Threads:</label>
                  <div className="text-lg font-semibold text-green-600 bg-green-50 px-2 py-1 rounded border">{getCurrentThreadCounts().weftThreads}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Weft Repeats:</label>
                  <input
                    type="text"
                    value={weftRepeats}
                    onChange={(e) => handleNumberInput(e.target.value, setWeftRepeats, false)}
                    className="w-full px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    placeholder="Enter weft repeats"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Total Weft Threads:</label>
                  <div className="text-lg font-semibold text-green-600 bg-green-50 px-2 py-1 rounded border">{calculateTotalWeftThreads()}</div>
                </div>
              </div>

              {/* Overall Thread Count */}
              <div className="text-center mb-4">
                <div className="bg-purple-50 border border-purple-200 rounded p-3 inline-block">
                  <span className="text-sm font-medium text-purple-700">Overall Thread Count: </span>
                  <span className="text-lg font-semibold text-purple-800">{calculateOverallThreadCount()}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer Buttons */}
        <div className="border-t border-gray-300 px-6 py-3 flex justify-between">
          <button 
            onClick={handleSaveDesign}
            className="px-6 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Save
          </button>
          <button
            onClick={() => setShowDesignSpecification(false)}
            className="px-6 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
          >
            Exit
          </button>
        </div>
      </div>

      {/* Calculation Results Dialog */}
      <CalculationResultsDialog
        showResults={showCalculationResults}
        setShowResults={setShowCalculationResults}
        calculationResults={calculationResults}
        designNumber={currentDesignNo || designNumber}
      />
    </div>
  )
}

export default DesignSpecificationDialog
