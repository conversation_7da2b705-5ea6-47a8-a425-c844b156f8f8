import React from 'react'
import {
  getToolButtonClass,
  getDrawingModeButtonClass,
  getEraserButtonClass,
  getActionButtonClass,
  getClearButtonClass
} from '../../utils/buttonStyles'

const LeftSidebar = ({
  selectedTool,
  setSelectedTool,
  isDrawingMode,
  setIsDrawingMode,
  isEraserMode,
  setIsEraserMode,
  undo,
  redo,
  clearCanvas,
  canUndo,
  canRedo,
  showColorPicker,
  setShowColorPicker,
  drawingModeInfoShown,
  setDrawingModeInfoShown
}) => {
  return (
    <div className="h-full bg-gray-100 p-2 overflow-y-auto">
      {/* Tools Section */}
      <div className="mb-4">
        <h3 className="text-sm font-semibold mb-2 text-gray-700">Tools</h3>
        <div className="space-y-1">
          <div className="text-xs text-gray-600">Drawing Mode</div>
          <div className="grid grid-cols-1 gap-1">
            <button
              onClick={() => setSelectedTool('Warp')}
              className={getToolButtonClass(selectedTool === 'Warp')}
            >
              Warp (Vertical)
            </button>
            <button
              onClick={() => setSelectedTool('Weft')}
              className={getToolButtonClass(selectedTool === 'Weft')}
            >
              Weft (Horizontal)
            </button>
          </div>
        </div>
      </div>

      {/* Drawing Mode Toggle */}
      <div className="mb-4">
        <button
          onClick={() => {
            if (!isDrawingMode) {
              // Entering drawing mode
              if (!drawingModeInfoShown) {
                // First time entering drawing mode - show thread information popup
                setShowColorPicker(true)
                setDrawingModeInfoShown(true)
              } else {
                // Subsequent times - go directly to drawing mode
                setIsDrawingMode(true)
              }
            } else {
              // Exiting drawing mode
              setIsDrawingMode(false)
            }
            if (isEraserMode) setIsEraserMode(false)
          }}
          className={getDrawingModeButtonClass(isDrawingMode)}
        >
          {isDrawingMode ? 'Exit Drawing Mode' : 'Enter Drawing Mode'}
        </button>
      </div>

      {/* Eraser Mode Toggle */}
      <div className="mb-4">
        <button
          onClick={() => {
            setIsEraserMode(!isEraserMode)
            if (isDrawingMode) setIsDrawingMode(false)
          }}
          className={getEraserButtonClass(isEraserMode)}
        >
          {isEraserMode ? 'Exit Eraser Mode' : 'Enter Eraser Mode'}
        </button>
      </div>

      {/* Undo/Redo Actions */}
      <div className="mb-4">
        <h3 className="text-sm font-semibold mb-2 text-gray-700">Actions</h3>
        <div className="grid grid-cols-2 gap-2 mb-2">
          <button
            onClick={undo}
            disabled={!canUndo}
            className={getActionButtonClass(!canUndo)}
          >
            Undo
          </button>
          <button
            onClick={redo}
            disabled={!canRedo}
            className={getActionButtonClass(!canRedo)}
          >
            Redo
          </button>
        </div>
        <button
          onClick={clearCanvas}
          className={getClearButtonClass()}
        >
          Clear Canvas
        </button>
      </div>
    </div>
  )
}

export default LeftSidebar
