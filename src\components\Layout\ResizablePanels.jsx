import React, { useState, useRef, useCallback, useEffect } from 'react'

const ResizablePanels = ({
  leftPanel,
  centerPanel,
  rightPanel,
  initialLeftWidth = 20, // percentage
  initialRightWidth = 20, // percentage
  minPanelWidth = 15, // minimum percentage
  maxPanelWidth = 70, // maximum percentage
  onResize = () => {} // callback when panels are resized
}) => {
  const [leftWidth, setLeftWidth] = useState(initialLeftWidth)
  const [rightWidth, setRightWidth] = useState(initialRightWidth)
  const [isDragging, setIsDragging] = useState(null) // 'left' or 'right'
  const containerRef = useRef(null)
  const startXRef = useRef(0)
  const startWidthRef = useRef({ left: 0, right: 0 })

  // Calculate center width
  const centerWidth = 100 - leftWidth - rightWidth

  const handleMouseDown = useCallback((side, e) => {
    e.preventDefault()
    setIsDragging(side)
    startXRef.current = e.clientX
    startWidthRef.current = { left: leftWidth, right: rightWidth }
    
    // Add no-select class to body to prevent text selection
    document.body.classList.add('no-select')
  }, [leftWidth, rightWidth])

  const handleMouseMove = useCallback((e) => {
    if (!isDragging || !containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const containerWidth = containerRect.width
    const deltaX = e.clientX - startXRef.current
    const deltaPercent = (deltaX / containerWidth) * 100

    if (isDragging === 'left') {
      const newLeftWidth = Math.max(
        minPanelWidth,
        Math.min(maxPanelWidth, startWidthRef.current.left + deltaPercent)
      )
      
      // Ensure center panel doesn't get too small
      const maxAllowedLeft = 100 - rightWidth - minPanelWidth
      const finalLeftWidth = Math.min(newLeftWidth, maxAllowedLeft)
      
      setLeftWidth(finalLeftWidth)
      // Trigger resize callback after a short delay to allow DOM to update
      setTimeout(() => onResize(), 0)
    } else if (isDragging === 'right') {
      const newRightWidth = Math.max(
        minPanelWidth,
        Math.min(maxPanelWidth, startWidthRef.current.right - deltaPercent)
      )

      // Ensure center panel doesn't get too small
      const maxAllowedRight = 100 - leftWidth - minPanelWidth
      const finalRightWidth = Math.min(newRightWidth, maxAllowedRight)

      setRightWidth(finalRightWidth)
      // Trigger resize callback after a short delay to allow DOM to update
      setTimeout(() => onResize(), 0)
    }
  }, [isDragging, leftWidth, rightWidth, minPanelWidth, maxPanelWidth])

  const handleMouseUp = useCallback(() => {
    setIsDragging(null)
    document.body.classList.remove('no-select')
  }, [])

  // Add global mouse event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // Prevent context menu on resize handles
  const handleContextMenu = useCallback((e) => {
    e.preventDefault()
  }, [])

  return (
    <div 
      ref={containerRef}
      className={`flex h-full ${isDragging ? 'dragging' : ''}`}
    >
      {/* Left Panel */}
      <div 
        className="resizable-panel overflow-hidden"
        style={{ width: `${leftWidth}%` }}
      >
        {leftPanel}
      </div>

      {/* Left Resize Handle */}
      <div
        className={`resize-handle ${isDragging === 'left' ? 'dragging' : ''}`}
        onMouseDown={(e) => handleMouseDown('left', e)}
        onContextMenu={handleContextMenu}
        title="Drag to resize left panel"
      />

      {/* Center Panel */}
      <div 
        className="resizable-panel flex-1 overflow-hidden"
        style={{ width: `${centerWidth}%` }}
      >
        {centerPanel}
      </div>

      {/* Right Resize Handle */}
      <div
        className={`resize-handle ${isDragging === 'right' ? 'dragging' : ''}`}
        onMouseDown={(e) => handleMouseDown('right', e)}
        onContextMenu={handleContextMenu}
        title="Drag to resize right panel"
      />

      {/* Right Panel */}
      <div 
        className="resizable-panel overflow-hidden"
        style={{ width: `${rightWidth}%` }}
      >
        {rightPanel}
      </div>
    </div>
  )
}

export default ResizablePanels
