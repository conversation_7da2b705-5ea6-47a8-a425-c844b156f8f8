import React from 'react'
import { getToolButtonClass } from '../../utils/buttonStyles'

const RightSidebar = ({
  selectedTool,
  setSelectedTool,
  selectedColor,
  selectedColorName,
  numberOfThreads,
  setNumberOfThreads,
  gridSize,
  stageRef,
  isDrawingMode,
  setIsDrawingMode,
  colorPalette,
  colorNames,
  handleColorSelect,
  designInfoCollected,
  setPendingColor,
  setPendingColorName,
  setShowDesignInfo,
  setCustomColor,
  setSelectedColorName,
  setShowColorPicker
}) => {
  return (
    <div className="h-full bg-gray-100 p-2 overflow-y-auto">
      {/* Colors Section */}
      <div className="mb-4">
        <h3 className="text-sm font-semibold mb-2 text-gray-700">Colors</h3>
        <div className="mb-2">
          <label className="text-xs text-gray-600">Color Facets:</label>
        </div>
        <div className="grid grid-cols-5 gap-1 mb-3">
          {colorPalette.map((color, index) => (
            <button
              key={index}
              onClick={() => handleColorSelect(color)}
              className={`w-8 h-8 rounded border-2 ${
                selectedColor === color ? 'border-black' : 'border-gray-300'
              }`}
              style={{ backgroundColor: color }}
              title={colorNames[color] || color}
            />
          ))}
        </div>
        <div className="flex justify-between text-xs">
          <button
            onClick={() => {
              if (!designInfoCollected) {
                // First time color selection - show design info dialog first
                setPendingColor(selectedColor)
                setPendingColorName('CUSTOM')
                setShowDesignInfo(true)
              } else {
                // Subsequent color selections - go directly to thread info
                setCustomColor(selectedColor)
                setSelectedColorName(colorNames[selectedColor] || 'CUSTOM')
                setShowColorPicker(true)
              }
            }}
            className="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded border text-gray-700"
          >
            Custom Color...
          </button>
          <button className="text-blue-600 hover:underline">Thread Settings...</button>
        </div>
      </div>

      {/* Yarn Preview */}
      <div className="mb-4">
        <h3 className="text-sm font-semibold mb-2 text-gray-700">Yarn Preview</h3>
        <div className="bg-white border border-gray-300 p-2 rounded">
          <div className="flex space-x-2 mb-2">
            <div
              className="w-8 h-16 rounded border border-gray-300"
              style={{ backgroundColor: selectedTool === 'Warp' ? selectedColor : '#000000' }}
            ></div>
            <div
              className="w-8 h-16 rounded border border-gray-300"
              style={{ backgroundColor: selectedTool === 'Weft' ? selectedColor : '#ffffff' }}
            ></div>
          </div>
          
          <div className="grid grid-cols-2 gap-1 mb-2">
            <button
              onClick={() => setSelectedTool('Warp')}
              className={getToolButtonClass(selectedTool === 'Warp')}
            >
              Vertical
            </button>
            <button
              onClick={() => setSelectedTool('Weft')}
              className={getToolButtonClass(selectedTool === 'Weft')}
            >
              Horizontal
            </button>
          </div>

          <div className="space-y-2">
            <div>
              <label className="text-xs text-gray-600">Number of threads:</label>
              <input
                type="number"
                value={numberOfThreads}
                onChange={(e) => setNumberOfThreads(parseInt(e.target.value) || 1)}
                min="1"
                max="50"
                className="w-full text-xs p-1 border border-gray-300 rounded mt-1"
              />
            </div>

            <div className="text-xs text-gray-600 border-t pt-2">
              <div>Thread count: {numberOfThreads}</div>
              <div>Zoom: {stageRef.current ? Math.round(stageRef.current.scaleX() * 100) : 100}%</div>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Settings */}
      <div className="mb-4">
        <button className="w-full text-xs bg-gray-200 hover:bg-gray-300 p-2 rounded border">
          Edit Settings
        </button>
      </div>

      {/* Current Color */}
      <div className="mb-4">
        <h3 className="text-sm font-semibold mb-2 text-gray-700">Current Color</h3>
        <div
          className="w-full h-8 border border-gray-300 rounded"
          style={{ backgroundColor: selectedColor }}
        ></div>
      </div>

      {/* Drawing Mode Status */}
      {isDrawingMode && (
        <div className="mb-4">
          <div className="bg-green-100 border border-green-300 rounded p-2">
            <h3 className="text-sm font-semibold text-green-800 mb-1">Drawing Mode Active</h3>
            <div className="text-xs text-green-700 space-y-1">
              <div>• Click on canvas to draw {numberOfThreads} {selectedTool.toLowerCase()} thread(s)</div>
              <div>• Color: {selectedColorName}</div>
              <div>• Tool: {selectedTool} ({selectedTool === 'Warp' ? 'Vertical' : 'Horizontal'})</div>
              <div>• Threads will color entire axis lines</div>
            </div>
            <button
              onClick={() => setIsDrawingMode(false)}
              className="mt-2 px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
            >
              Exit Drawing Mode
            </button>
          </div>
        </div>
      )}

      {/* Canvas Controls */}
      <div>
        <h3 className="text-sm font-semibold mb-2 text-gray-700">Canvas Controls</h3>
        <div className="text-xs text-gray-600 space-y-1">
          <div>• Mouse wheel: Zoom in/out</div>
          <div>• Drag: Pan canvas {isDrawingMode ? '(disabled in drawing mode)' : ''}</div>
          <div>• Use zoom controls (top-right)</div>
          <div>• Gray axes show center (0,0)</div>
          <div>• Grid always aligned to axes</div>
          {isDrawingMode && <div>• Click to place threads on canvas</div>}
        </div>
      </div>
    </div>
  )
}

export default RightSidebar
