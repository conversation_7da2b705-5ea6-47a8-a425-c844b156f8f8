import React from 'react'

const TopMenuBar = ({
  showDesignMenu,
  setShowDesignMenu,
  showPurchaseMenu,
  setShowPurchaseMenu,
  showDyeingMenu,
  setShowDyeingMenu,
  showWeavingMenu,
  setShowWeavingMenu,
  showSuppliersMenu,
  setShowSuppliersMenu,
  showProcessingMenu,
  setShowProcessingMenu,
  showDispatchesMenu,
  setShowDispatchesMenu,
  showAccountsMenu,
  setShowAccountsMenu,
  showUtilityMenu,
  setShowUtilityMenu,
  handleCreateDesignSpecification,
  closeAllMenus,
  handleLogout,
  handleChangePassword
}) => {
  const menuItems = [
    'Purchase', 'Design', 'Dyeing', 'Weaving', 'Suppliers',
    'Processing', 'Dispatches', 'Accounts', 'Utility'
  ]

  const getMenuState = (item) => {
    switch (item) {
      case 'Purchase': return showPurchaseMenu
      case 'Design': return showDesignMenu
      case 'Dyeing': return showDyeingMenu
      case 'Weaving': return showWeavingMenu
      case 'Suppliers': return showSuppliersMenu
      case 'Processing': return showProcessingMenu
      case 'Dispatches': return showDispatchesMenu
      case 'Accounts': return showAccountsMenu
      case 'Utility': return showUtilityMenu
      default: return false
    }
  }

  const setMenuState = (item, state) => {
    switch (item) {
      case 'Purchase': setShowPurchaseMenu(state); break
      case 'Design': setShowDesignMenu(state); break
      case 'Dyeing': setShowDyeingMenu(state); break
      case 'Weaving': setShowWeavingMenu(state); break
      case 'Suppliers': setShowSuppliersMenu(state); break
      case 'Processing': setShowProcessingMenu(state); break
      case 'Dispatches': setShowDispatchesMenu(state); break
      case 'Accounts': setShowAccountsMenu(state); break
      case 'Utility': setShowUtilityMenu(state); break
    }
  }

  const handleMenuClick = (item) => {
    closeAllMenus()
    setMenuState(item, !getMenuState(item))
  }

  return (
    <div className="bg-gray-200 border-b border-gray-300 px-2 py-1">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-1">
        {menuItems.map((item, index) => (
          <div key={index} className="relative">
            {item === 'Design' ? (
              <div className="design-menu-container menu-container">
                <button
                  onClick={() => handleMenuClick('Design')}
                  className={`px-3 py-1 text-sm hover:bg-gray-300 rounded ${
                    showDesignMenu ? 'bg-gray-300' : ''
                  }`}
                >
                  {item}
                </button>
                {showDesignMenu && (
                  <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-48">
                    <div className="py-1">
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-blue-500 hover:text-white bg-blue-500 text-white">
                        New Design Entry
                      </button>
                      <button
                        onClick={handleCreateDesignSpecification}
                        className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100"
                      >
                        Create Design Specification
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Design Copy
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Colour Changes
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 text-orange-600">
                        Fabric Costing
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 text-orange-600">
                        Sample Costing
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Design Finished
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Design-New-Old
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Calculation Constant
                      </button>
                      <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100">
                        Yarn Requirement
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : item === 'Weaving' ? (
              <div className="menu-container">
                <button
                  onClick={() => handleMenuClick(item)}
                  className={`px-3 py-1 text-sm hover:bg-gray-300 rounded ${
                    getMenuState(item) ? 'bg-gray-300' : ''
                  }`}
                >
                  {item}
                </button>
                {getMenuState(item) && (
                  <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-48">
                    <div className="py-1">
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Weaver Order Entry
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        New Weaver Entry
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Print Reports
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : item === 'Dispatches' ? (
              <div className="menu-container">
                <button
                  onClick={() => handleMenuClick(item)}
                  className={`px-3 py-1 text-sm hover:bg-gray-300 rounded ${
                    getMenuState(item) ? 'bg-gray-300' : ''
                  }`}
                >
                  {item}
                </button>
                {getMenuState(item) && (
                  <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-48">
                    <div className="py-1">
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Buyer Order Entry
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Buyer Order List
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Despatch Entry
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Invoice Entry
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Despatch List
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Sales Returns
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Debit Note
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Bill wise Payments
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Export Invoices
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Fabric Purchase
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Cash Sales
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        New Buyer Entry
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        New Agent Entry
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Print Reports
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : item === 'Utility' ? (
              <div className="menu-container">
                <button
                  onClick={() => handleMenuClick(item)}
                  className={`px-3 py-1 text-sm hover:bg-gray-300 rounded ${
                    getMenuState(item) ? 'bg-gray-300' : ''
                  }`}
                >
                  {item}
                </button>
                {getMenuState(item) && (
                  <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-48">
                    <div className="py-1">
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Initialize Account Year
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Year Opening Stock Entry
                      </button>
                      <button
                        onClick={() => {
                          handleChangePassword()
                          closeAllMenus()
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        Change Password
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Transfer Closing Balance
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Edit Terms
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Edit Company Details
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Backup
                      </button>
                      <div className="border-t border-gray-200 my-1"></div>
                      <button
                        onClick={() => {
                          handleLogout()
                          closeAllMenus()
                        }}
                        className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700"
                      >
                        Logout
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="menu-container">
                <button
                  onClick={() => handleMenuClick(item)}
                  className={`px-3 py-1 text-sm hover:bg-gray-300 rounded ${
                    getMenuState(item) ? 'bg-gray-300' : ''
                  }`}
                >
                  {item}
                </button>
                {getMenuState(item) && (
                  <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 min-w-48">
                    <div className="py-1">
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        {item} Option 1
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        {item} Option 2
                      </button>
                      <button className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        {item} Option 3
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
        </div>
      </div>
    </div>
  )
}

export default TopMenuBar
