import React from 'react';

const DesignPrintTemplate = ({ design, threadAnalysis }) => {
  const { warp, weft } = threadAnalysis;
  const totalWarpEnds = warp.reduce((total, w) => total + parseInt(w.ends || 0), 0);
  const totalWeftEnds = weft.reduce((total, w) => total + parseInt(w.ends || 0), 0);
  const warpRepeats = 119; // This would be dynamic in a real implementation
  const totalThreads = totalWarpEnds * warpRepeats;

  return (
    <div className="print-template font-mono text-xs p-8 bg-white" style={{ fontFamily: 'Courier, monospace' }}>
      <div className="text-center mb-6">
        <h1 className="text-lg font-bold">THILAGA IMPEX</h1>
        <h2 className="text-md font-bold mt-2">DESIGN SPECIFICATION</h2>
      </div>

      <div className="flex justify-between mb-4">
        <div className="w-1/2">
          <div className="mb-1">
            <span className="inline-block w-24">DESIGN NO. :</span>
            <span className="font-bold">{design.designNumber}</span>
          </div>
          <div className="mb-1">
            <span className="inline-block w-24">REED :</span>
            <span className="font-bold">{design.reedValue}</span>
          </div>
          <div className="mb-1">
            <span className="inline-block w-24">PICKS :</span>
            <span className="font-bold">{design.picksValue}</span>
          </div>
          <div className="mb-1">
            <span className="inline-block w-24">WIDTH :</span>
            <span className="font-bold">{design.widWrpValue}</span>
          </div>
        </div>
        <div className="w-1/2">
          <div className="mb-1">
            <span className="inline-block w-24">COLOUR :</span>
            <span className="font-bold">
              {warp.map(w => w.colour).filter((c, i, arr) => arr.indexOf(c) === i).join(', ')}
            </span>
          </div>
          <div className="mb-1">
            <span className="inline-block w-24">WARP COUNT :</span>
            <span className="font-bold">
              {warp.map(w => w.count).filter((c, i, arr) => arr.indexOf(c) === i).join(', ')}
            </span>
          </div>
          <div className="mb-1">
            <span className="inline-block w-24">WEFT COUNT :</span>
            <span className="font-bold">
              {weft.map(w => w.count).filter((c, i, arr) => arr.indexOf(c) === i).join(', ')}
            </span>
          </div>
          <div className="mb-1">
            <span className="inline-block w-24">WEAVE TYPE :</span>
            <span className="font-bold">seer sucker</span>
          </div>
        </div>
        <div className="absolute right-8 top-20">
          <div>PAGE : 1</div>
        </div>
      </div>

      {/* BASE WARP Section */}
      <div className="mt-8">
        <h3 className="font-bold mb-2">BASE WARP</h3>
        <div className="flex mb-1">
          <div className="w-16 text-left">COUNT</div>
          <div className="w-32 text-left">COLOUR</div>
          <div className="w-16 text-left">ENDS</div>
          <div className="w-16 text-left">SETS</div>
        </div>
        {warp.map((item, index) => (
          <div key={`warp-${index}`} className="flex mb-1">
            <div className="w-16 text-left">{index + 1} {item.count}</div>
            <div className="w-32 text-left">{item.colour}{'.'}{'.'.repeat(10)}</div>
            <div className="w-16 text-left">{item.ends}</div>
            <div className="w-16 text-left">{item.set}</div>
          </div>
        ))}
        <div className="mt-2 flex mb-1">
          <div className="w-48 text-left">TOTAL</div>
          <div className="w-16 text-left">{totalWarpEnds} X{warpRepeats}={totalThreads}</div>
        </div>
        <div className="flex mb-1">
          <div className="w-48 text-left">EXTRA THREADS</div>
          <div className="w-16 text-left">0</div>
        </div>
        <div className="flex mb-1">
          <div className="w-48 text-left">TOTAL THREADS</div>
          <div className="w-16 text-left">{totalThreads}</div>
        </div>
      </div>

      {/* WEFT Section */}
      <div className="mt-8">
        <h3 className="font-bold mb-2">WEFT</h3>
        <div className="flex mb-1">
          <div className="w-16 text-left">COUNT</div>
          <div className="w-32 text-left">COLOUR</div>
          <div className="w-16 text-left">ENDS</div>
          <div className="w-16 text-left">SETS</div>
        </div>
        {weft.map((item, index) => (
          <div key={`weft-${index}`} className="flex mb-1">
            <div className="w-16 text-left">{index + 1} {item.count}</div>
            <div className="w-32 text-left">{item.colour}{'.'}{'.'.repeat(10)}</div>
            <div className="w-16 text-left">{item.ends}</div>
            <div className="w-16 text-left">{item.set}</div>
          </div>
        ))}
        <div className="mt-2 flex mb-1">
          <div className="w-48 text-left">TOTAL THREADS</div>
          <div className="w-16 text-left">{totalWeftEnds}</div>
        </div>
      </div>
    </div>
  );
};

export default DesignPrintTemplate;
