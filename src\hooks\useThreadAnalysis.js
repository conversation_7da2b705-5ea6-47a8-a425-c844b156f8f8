import { useMemo } from 'react'
import { analyzeThreads, calculateThreadStats } from '../utils/threadUtils'

/**
 * Custom hook for thread analysis
 * @param {Array} drawnThreads - Array of thread objects from canvas
 * @returns {Object} Thread analysis data and statistics
 */
export const useThreadAnalysis = (drawnThreads) => {
  // Memoize thread analysis to avoid recalculation on every render
  const threadAnalysis = useMemo(() => {
    return analyzeThreads(drawnThreads)
  }, [drawnThreads])

  // Memoize thread statistics
  const threadStats = useMemo(() => {
    return calculateThreadStats(drawnThreads)
  }, [drawnThreads])

  // Memoize individual thread type data
  const warpData = useMemo(() => threadAnalysis.warp, [threadAnalysis])
  const weftData = useMemo(() => threadAnalysis.weft, [threadAnalysis])

  return {
    // Raw analysis data
    threadAnalysis,
    warpData,
    weftData,
    
    // Statistics
    threadStats,
    
    // Convenience getters
    totalWarpThreads: threadStats.totalWarpThreads,
    totalWeftThreads: threadStats.totalWeftThreads,
    warpColors: threadStats.warpColors,
    weftColors: threadStats.weftColors,
    totalThreads: threadStats.totalThreads,
    uniqueColors: threadStats.uniqueColors,
    
    // Helper functions
    hasWarpThreads: warpData.length > 0,
    hasWeftThreads: weftData.length > 0,
    hasAnyThreads: drawnThreads.length > 0
  }
}
