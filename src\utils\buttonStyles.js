// Shared button styling utilities

export const getToolButtonClass = (isSelected) => {
  return `px-2 py-1 text-xs rounded ${
    isSelected
      ? 'bg-blue-500 text-white'
      : 'bg-gray-200 hover:bg-gray-300'
  }`
}

export const getDrawingModeButtonClass = (isDrawingMode) => {
  return `w-full px-3 py-2 text-sm rounded border ${
    isDrawingMode
      ? 'bg-green-500 text-white border-green-600 hover:bg-green-600'
      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
  }`
}

export const getMenuButtonClass = (isActive) => {
  return `px-3 py-1 text-sm hover:bg-gray-300 rounded ${
    isActive ? 'bg-gray-300' : ''
  }`
}

export const getEraserButtonClass = (isEraserMode) => {
  return `w-full px-3 py-2 text-sm rounded border ${
    isEraserMode
      ? 'bg-red-500 text-white border-red-600 hover:bg-red-600'
      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
  }`
}

export const getActionButtonClass = (isDisabled = false) => {
  return `px-3 py-1 text-xs rounded border ${
    isDisabled
      ? 'bg-gray-200 text-gray-400 border-gray-300 cursor-not-allowed'
      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
  }`
}

export const getClearButtonClass = () => {
  return 'w-full px-3 py-2 text-sm rounded border bg-orange-500 text-white border-orange-600 hover:bg-orange-600'
}
