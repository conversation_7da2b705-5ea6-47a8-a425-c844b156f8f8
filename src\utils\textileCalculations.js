// Textile calculation utilities for yarn and thread calculations

/**
 * Constants for textile calculations
 */
export const TEXTILE_CONSTANTS = {
  CRIMP_DYEING: 5, // 5% crimp in dyeing
  CRIMP_WRAPPING: 7, // 7% crimp in wrapping
  BUNDLE_WEIGHT: 4.54, // 1 bundle = 4.54 kgs
  CONE_CONST_VALUE: 7685, // Constant value for cone calculations
  DEFAULT_COUNT_VALUE: 60, // Default count value for calculations
}

/**
 * Calculate excess percentage using formula: a = a + (x*a/100)
 * @param {number} baseValue - Base value (a)
 * @param {number} percentage - Percentage to add (x)
 * @returns {number} Calculated value with excess percentage
 */
export const calculateExcessPercentage = (baseValue, percentage) => {
  if (!baseValue || !percentage) return baseValue || 0
  return baseValue + (percentage * baseValue / 100)
}

/**
 * Calculate crimp adjustments for dyeing and wrapping
 * @param {number} baseValue - Base value to adjust
 * @param {string} processType - 'dyeing' or 'wrapping'
 * @returns {number} Adjusted value with crimp
 */
export const calculateCrimp = (baseValue, processType = 'dyeing') => {
  if (!baseValue) return 0
  const crimpPercentage = processType === 'wrapping' ? TEXTILE_CONSTANTS.CRIMP_WRAPPING : TEXTILE_CONSTANTS.CRIMP_DYEING
  return calculateExcessPercentage(baseValue, crimpPercentage)
}

/**
 * Calculate cone requirements for warp threads
 * @param {number} count - Thread count (e.g., 2/60 = 30)
 * @param {number} bundleWeight - Weight per bundle (default: 4.54 kgs)
 * @param {number} constValue - Constant value (default: 7685)
 * @returns {object} Cone calculation results
 */
export const calculateConeRequirement = (count, bundleWeight = TEXTILE_CONSTANTS.BUNDLE_WEIGHT, constValue = TEXTILE_CONSTANTS.CONE_CONST_VALUE) => {
  if (!count || count === 0) return { countValue: 0, coneRequirement: 0 }
  
  const countValue = bundleWeight / count
  const coneRequirement = constValue / count
  
  return {
    countValue: parseFloat(countValue.toFixed(4)),
    coneRequirement: parseFloat(coneRequirement.toFixed(2))
  }
}

/**
 * Calculate warp yarn requirements
 * @param {number} read - Number of lines in an inch (read value)
 * @param {number} agalam - Agalam value
 * @param {number} warpPercentage - Warp percentage to add
 * @param {number} count - Thread count
 * @returns {object} Warp calculation results
 */
export const calculateWarpYarn = (read, agalam, warpPercentage = 20, count = TEXTILE_CONSTANTS.DEFAULT_COUNT_VALUE) => {
  if (!read || !agalam) return { totalLeaf: 0, withCrimp: 0, withPercentage: 0, coneData: null }
  
  // Basic calculation: read * agalam = total leaf
  const totalLeaf = read * agalam
  
  // Add crimp for wrapping (7%)
  const withCrimp = calculateCrimp(totalLeaf, 'wrapping')
  
  // Add warp percentage
  const withPercentage = calculateExcessPercentage(withCrimp, warpPercentage)
  
  // Calculate cone requirements
  const coneData = calculateConeRequirement(count)
  
  return {
    totalLeaf: parseFloat(totalLeaf.toFixed(2)),
    withCrimp: parseFloat(withCrimp.toFixed(2)),
    withPercentage: parseFloat(withPercentage.toFixed(2)),
    coneData
  }
}

/**
 * Calculate weft yarn requirements
 * @param {number} pick - Pick value (weft measurement)
 * @param {number} meters - Length in meters
 * @param {number} weftPercentage - Weft percentage to add
 * @param {number} count - Thread count
 * @returns {object} Weft calculation results
 */
export const calculateWeftYarn = (pick, meters, weftPercentage = 6, count = TEXTILE_CONSTANTS.DEFAULT_COUNT_VALUE) => {
  if (!pick || !meters) return { totalYarn: 0, withCrimp: 0, withPercentage: 0 }
  
  // Basic calculation for weft
  const totalYarn = pick * meters
  
  // Add crimp for dyeing (5%)
  const withCrimp = calculateCrimp(totalYarn, 'dyeing')
  
  // Add weft percentage
  const withPercentage = calculateExcessPercentage(withCrimp, weftPercentage)
  
  return {
    totalYarn: parseFloat(totalYarn.toFixed(2)),
    withCrimp: parseFloat(withCrimp.toFixed(2)),
    withPercentage: parseFloat(withPercentage.toFixed(2))
  }
}

/**
 * Calculate individual thread requirements for each color
 * @param {Array} threadAnalysis - Thread analysis data (warp/weft)
 * @param {object} calculationParams - Calculation parameters
 * @returns {object} Individual thread calculations
 */
export const calculateIndividualThreads = (threadAnalysis, calculationParams) => {
  const { length, warpPercentage, weftPercentage, read, agalam } = calculationParams
  
  const warpCalculations = threadAnalysis.warp.map(thread => {
    const warpData = calculateWarpYarn(read, agalam, warpPercentage, parseInt(thread.count.split('/')[1]) || TEXTILE_CONSTANTS.DEFAULT_COUNT_VALUE)
    return {
      ...thread,
      calculations: warpData,
      yarnRequired: warpData.withPercentage * thread.ends
    }
  })
  
  const weftCalculations = threadAnalysis.weft.map(thread => {
    const weftData = calculateWeftYarn(thread.ends, length, weftPercentage, parseInt(thread.count.split('/')[1]) || TEXTILE_CONSTANTS.DEFAULT_COUNT_VALUE)
    return {
      ...thread,
      calculations: weftData,
      yarnRequired: weftData.withPercentage
    }
  })
  
  return {
    warp: warpCalculations,
    weft: weftCalculations
  }
}

/**
 * Format calculation results for display
 * @param {object} calculations - Calculation results
 * @returns {object} Formatted results
 */
export const formatCalculationResults = (calculations) => {
  const totalWarpYarn = calculations.warp.reduce((sum, thread) => sum + thread.yarnRequired, 0)
  const totalWeftYarn = calculations.weft.reduce((sum, thread) => sum + thread.yarnRequired, 0)
  
  return {
    ...calculations,
    totals: {
      warpYarn: parseFloat(totalWarpYarn.toFixed(2)),
      weftYarn: parseFloat(totalWeftYarn.toFixed(2)),
      totalYarn: parseFloat((totalWarpYarn + totalWeftYarn).toFixed(2))
    }
  }
}
