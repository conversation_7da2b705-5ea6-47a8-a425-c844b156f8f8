// Utility functions for thread analysis and processing

/**
 * Analyze drawn threads and create data for WARP and WEFT tables
 * @param {Array} drawnThreads - Array of thread objects from canvas
 * @returns {Object} Object containing warp and weft thread analysis
 */
export const analyzeThreads = (drawnThreads) => {
  const warpThreads = drawnThreads.filter(thread => thread.type === 'Warp')
  const weftThreads = drawnThreads.filter(thread => thread.type === 'Weft')

  const analyzeByType = (threads) => {
    const colorGroups = {}
    
    threads.forEach(thread => {
      const colorKey = thread.color
      if (!colorGroups[colorKey]) {
        colorGroups[colorKey] = {
          color: thread.color,
          colorName: thread.colorName,
          count: 0
        }
      }
      colorGroups[colorKey].count++
    })

    return Object.values(colorGroups).map((group, index) => ({
      group: index + 1,
      count: '60/2', // Dummy thread thickness
      colour: group.colorName,
      colorHex: group.color,
      ends: group.count,
      set: 1,
      seer: 'N'
    }))
  }

  return {
    warp: analyzeByType(warpThreads),
    weft: analyzeByType(weftThreads)
  }
}

/**
 * Calculate thread statistics
 * @param {Array} drawnThreads - Array of thread objects from canvas
 * @returns {Object} Thread statistics
 */
export const calculateThreadStats = (drawnThreads) => {
  const analysis = analyzeThreads(drawnThreads)
  
  return {
    totalWarpThreads: analysis.warp.reduce((sum, row) => sum + row.ends, 0),
    totalWeftThreads: analysis.weft.reduce((sum, row) => sum + row.ends, 0),
    warpColors: analysis.warp.length,
    weftColors: analysis.weft.length,
    totalThreads: drawnThreads.length,
    uniqueColors: [...new Set(drawnThreads.map(thread => thread.color))].length
  }
}

/**
 * Helper functions for input validation and formatting
 */
export const inputValidation = {
  /**
   * Handle number input with validation
   * @param {string} value - Input value
   * @param {Function} setter - State setter function
   * @param {boolean} isDecimal - Whether to allow decimal numbers
   */
  handleNumberInput: (value, setter, isDecimal = false) => {
    if (isDecimal) {
      // Allow decimal numbers
      const regex = /^\d*\.?\d*$/
      if (regex.test(value) || value === '') {
        setter(value)
      }
    } else {
      // Allow only integers
      const regex = /^\d*$/
      if (regex.test(value) || value === '') {
        setter(value)
      }
    }
  },

  /**
   * Format decimal value to 2 decimal places
   * @param {string} value - Value to format
   * @returns {string} Formatted value
   */
  formatDecimalValue: (value) => {
    if (value === '' || isNaN(parseFloat(value))) return value
    return parseFloat(value).toFixed(2)
  }
}

/**
 * Calculate total threads based on specification values
 * @param {string} reedValue - Reed value
 * @param {string} repeatsValue - Repeats value
 * @param {string} extraValue - Extra value
 * @returns {number} Total calculated threads
 */
export const calculateTotal = (reedValue, repeatsValue, extraValue) => {
  const totalThreadsPerRepeat = parseInt(reedValue) || 0
  const repeats = parseInt(repeatsValue) || 0
  const extra = parseInt(extraValue) || 0
  return totalThreadsPerRepeat * repeats + extra
}

/**
 * Default values for design specification form
 */
export const defaultSpecificationValues = {
  reedValue: '45',
  picksValue: '0',
  widWrpValue: '20.00',
  wftValue: '20.00',
  repeatsValue: '',
  extraValue: ''
}

/**
 * Reset design specification form to defaults
 * @param {Object} setters - Object containing all setter functions
 */
export const resetDesignSpecification = (setters) => {
  const { 
    setReedValue, 
    setPicksValue, 
    setWidWrpValue, 
    setWftValue, 
    setRepeatsValue, 
    setExtraValue 
  } = setters

  setReedValue(defaultSpecificationValues.reedValue)
  setPicksValue(defaultSpecificationValues.picksValue)
  setWidWrpValue(defaultSpecificationValues.widWrpValue)
  setWftValue(defaultSpecificationValues.wftValue)
  setRepeatsValue(defaultSpecificationValues.repeatsValue)
  setExtraValue(defaultSpecificationValues.extraValue)
}
